import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart' hide Trans;
import 'package:gopal/core/localization/strings.dart';
import 'package:gopal/core/models/activity/constants/type.dart';
import 'package:gopal/core/services/state_management/widgets/obs_widget.dart';
import 'package:gopal/core/style/repo.dart';
import 'package:gopal/core/utils/num_utils.dart';
import 'package:gopal/features/widgets/general_componenets/svg_icon.dart';

import '../../widgets/periodic_time_info.dart';
import '../controller.dart';
import '../models/activity.dart';

class ActivityInfoColumn extends StatelessWidget {
  final ActivityDetails activity;
  const ActivityInfoColumn({super.key, required this.activity});

  @override
  Widget build(BuildContext context) {
    ActivityDetailsPageController controller = Get.find(tag: "${activity.id}");
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _RowInfo(
            icon: Assets.icons.price.path,
            label: "${LocaleKeys.activity_price.tr()}: ",
            value:
                "${activity.price.priceRound} ${LocaleKeys.s_a_r.tr()} ${activity.priceType.explainedText}",
            trailing:
                activity.priceBeforeDiscount != null
                    ? [
                      const WidgetSpan(child: SizedBox(width: 12)),
                      TextSpan(
                        text: activity.priceBeforeDiscount!.priceRound,
                        style: const TextStyle(
                          decoration: TextDecoration.lineThrough,
                        ),
                      ),
                    ]
                    : null,
          ),
          if (activity.type == ActivityType.periodic_days) ...[
            const Gap(16),
            PeriodicTimeInfo(periodic: activity.periodic!),
          ],
          if (activity.type == ActivityType.ticket) ...[
            Gap(12),
            _RowInfo(
              icon: Assets.icons.calendar.path,
              label: "${LocaleKeys.activity_duration.tr()}: ",
              value:
                  "${LocaleKeys.for_days.plural(activity.ticketExpirationDuration!, args: ["${activity.ticketExpirationDuration}"])} ${LocaleKeys.starting_from_the_date_of_joining.tr()} ",
            ),
          ],
          if (activity.type == ActivityType.numbered_session)
            ObsListBuilder(
              obs: controller.sets,
              loader: (_) => const SizedBox(),
              errorBuilder: (_, __) => const SizedBox(),
              builder: (context, sets) {
                if (sets.length == 1 && sets.first.sessions.length == 1) {
                  return Column(
                    children: [
                      const Gap(12),
                      _RowInfo(
                        icon: Assets.icons.calendar.path,
                        label: "${LocaleKeys.activity_date.tr()}: ",
                        value: DateFormat.yMd().format(
                          sets.first.sessions.first.date,
                        ),
                      ),
                      const Gap(12),
                      _RowInfo(
                        icon: Assets.icons.time.path,
                        label: "${LocaleKeys.time.tr()}: ",
                        value: LocaleKeys.from_to.tr(
                          args: [
                            sets.first.sessions.first.from.format(context),
                            sets.first.sessions.first.to.format(context),
                          ],
                        ),
                      ),
                    ],
                  );
                }
                return SizedBox();
              },
            ),
          const Gap(16),
          _RowInfo(
            icon: Assets.icons.targetAge.path,
            label: "${LocaleKeys.target_age.tr()}: ",
            value: LocaleKeys.from_to.tr(
              args: ["${activity.minAge}", "${activity.maxAge}"],
            ),
          ),
          const Gap(16),
          _CapacityInfo(activity: activity),
          const Gap(16),
          _GenderInfo(activity: activity),
          if (activity.isFamily) ...[const Gap(16), _FamilyInfo()],
          if (activity.season != null) ...[
            const Gap(16),
            _SeasonInfo(activity: activity),
          ],

          if (activity.specialCases.isNotEmpty) ...[
            const Gap(16),
            _SpecialCaseInfo(activity: activity),
          ],
        ],
      ),
    );
  }
}

class _CapacityInfo extends StatelessWidget {
  const _CapacityInfo({required this.activity});

  final ActivityDetails activity;

  @override
  Widget build(BuildContext context) {
    return _RowInfo(
      icon: Assets.icons.chairs.path,
      label: "${LocaleKeys.capacity.tr()}: ",
      value: LocaleKeys.seats.plural(
        activity.capacity,
        args: ["${activity.capacity}"],
      ),
    );
  }
}

class _GenderInfo extends StatelessWidget {
  const _GenderInfo({required this.activity});

  final ActivityDetails activity;

  @override
  Widget build(BuildContext context) {
    return _RowInfo(
      icon: Assets.icons.gender.path,
      label: "${LocaleKeys.gender.tr()}: ",
      value: activity.gender.text,
    );
  }
}

class _SpecialCaseInfo extends StatelessWidget {
  const _SpecialCaseInfo({required this.activity});

  final ActivityDetails activity;

  @override
  Widget build(BuildContext context) {
    return _RowInfo(
      icon: Assets.icons.brain.path,
      label: "${LocaleKeys.special_case.tr()}: ",
      value: activity.specialCases.map((e) => e.name).join(", "),
    );
  }
}

class _SeasonInfo extends StatelessWidget {
  const _SeasonInfo({required this.activity});

  final ActivityDetails activity;

  @override
  Widget build(BuildContext context) {
    return _RowInfo(
      icon: Assets.icons.season.path,
      label: "${LocaleKeys.season.tr()}: ",
      value: activity.season!,
    );
  }
}

class _FamilyInfo extends StatelessWidget {
  const _FamilyInfo();

  @override
  Widget build(BuildContext context) {
    return _RowInfo(
      icon: Assets.icons.family.path,
      label: "${LocaleKeys.family_activity.tr()}: ",
      value: LocaleKeys.targeted_for_families.tr(),
    );
  }
}

class _RowInfo extends StatelessWidget {
  const _RowInfo({
    required this.icon,
    required this.label,
    required this.value,
    this.trailing,
  });

  final String icon;
  final String label;
  final String value;
  final List<InlineSpan>? trailing;

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        SvgIcon(icon, size: 20, color: StyleRepo.grey.shade700),
        const Gap(6),
        Expanded(
          child: RichText(
            text: TextSpan(
              style: context.textTheme.bodyMedium,
              children: [
                TextSpan(
                  text: label,
                  style: const TextStyle(fontWeight: FontWeight.w500),
                ),
                TextSpan(text: value),
                if (trailing != null) ...trailing!,
              ],
            ),
          ),
        ),
      ],
    );
  }
}
