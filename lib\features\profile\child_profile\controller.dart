// ignore_for_file: invalid_use_of_protected_member

import 'package:dio/dio.dart';
import 'package:get/get.dart';
import 'package:gopal/core/models/book/status.dart';
import 'package:gopal/core/models/user/main_child.dart';
import 'package:gopal/core/routes.dart';
import 'package:gopal/core/services/state_management/observable_variable.dart';

import '../../../core/services/rest_api/rest_api.dart';
import '../create_child/models/nav.dart';
import '../my_profile/controller.dart';
import 'models/calendar_session.dart';
import 'models/child.dart';
import 'models/nav.dart';

class ChildProfilePageController extends GetxController {
  final ChildProfilePageNav nav;
  ChildProfilePageController(this.nav);

  late ObsVar<ChildDetails> child = ObsVar(null);

  fetchChild() async {
    ResponseModel response = await APIService.instance.requestAPI(
      Request(
        endPoint: EndPoints.child_profile(nav.id),
        fromJson: ChildDetails.fromJson,
      ),
    );
    if (response.success) {
      child.value = response.data;
      loadSessions(selectedDate.year, selectedDate.month);
    } else {
      child.error = response.message;
    }
  }

  refreshData() async {
    await fetchChild();
  }

  @override
  void onInit() {
    fetchChild();
    super.onInit();
  }

  editChildProfile(ChildDetails child) async {
    MainChild? editedChild = await Nav.to(
      Pages.create_child,
      arguments: CreateChildPageNav(
        child: child.toChildCreation(),
        withAPI: true,
      ),
    );
    if (editedChild != null) {
      refreshData();
      try {
        Get.find<ProfilePageController>().updateChild(editedChild);
      } catch (_) {}
    }
  }

  //SECTION - Calendar

  CancelToken? cancel;

  loadSessions(int year, int month) async {
    calendarSessions.reset();
    cancel?.cancel();
    highlightedDays.value = {};

    cancel = CancelToken();

    ResponseModel response = await APIService.instance.requestAPI(
      Request(
        endPoint: EndPoints.child_sessions,
        params: {
          "children_ids[]": nav.id,
          "statuses[]": BookStatus.in_progress.value,
          "year": selectedDate.year,
          "month": selectedDate.month,
        },
      ),
    );
    if (response.success) {
      List<CalendarSession> sessions =
          response.data
              .map<CalendarSession>((e) => CalendarSession.fromJson(e))
              .toList();
      Map<int, List<CalendarSession>> map = {};
      for (var session in sessions) {
        if (map[session.date.day] == null) {
          map[session.date.day] = [];
        }
        map[session.date.day]!.add(session);
      }
      highlightedDays.value = map.keys.toSet();
      calendarSessions.value = map;
      if (calendarSessions.value!.isNotEmpty) {
        selectedDate = DateTime(
          selectedDate.year,
          selectedDate.month,
          calendarSessions.value!.keys.first,
        );
      }
    } else if (response.errorType is CANCELED) {
    } else {
      calendarSessions.error = response.message;
    }
  }

  ObsVar<Map<int, List<CalendarSession>>> calendarSessions = ObsVar(null);

  final Rx<DateTime> _selectedDate = DateTime.now().obs;
  DateTime get selectedDate => _selectedDate.value;
  set selectedDate(DateTime value) {
    _selectedDate.value = value;
    if (value.month != _selectedDate.value.month) {
      onMonthChanged(value, withSelectionDate: false);
    }
  }

  onMonthChanged(DateTime date, {bool withSelectionDate = true}) {
    if (withSelectionDate) {
      _selectedDate.value = date;
    }
    loadSessions(date.year, date.month);
  }

  refreshCalendar() {
    onMonthChanged(selectedDate, withSelectionDate: false);
  }

  RxSet<int> highlightedDays = RxSet();

  //!SECTION
}
