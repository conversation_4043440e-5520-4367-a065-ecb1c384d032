import 'package:gopal/core/models/category.dart';
import 'package:gopal/core/models/image.dart';
import 'package:gopal/core/models/selection.dart';
import 'package:gopal/core/models/activity/periodic/main_periodic.dart';
import 'package:gopal/features/activities/models/sub_activity_data.dart';

class ActivityDetails extends SubActivityData {
  late List<Selection> specialCases;
  late List<ImageModel> organizers;
  late List<ImageModel> media;
  late List<Category> categories;
  int? favouriteId;
  double? distance;
  int? ticketExpirationDuration;

  ActivityDetails({
    required super.id,
    required super.capacity,
    required super.name,
    required super.description,
    required super.center,
    required super.isAvailable,
    required super.isSupportCustom,
    super.discountType,
    super.discount,
    required super.activityId,
    required super.priceType,
    required super.price,
    required super.minAge,
    required super.maxAge,
    required super.gender,
    required super.isFamily,
    required super.type,
    required this.specialCases,
    required this.organizers,
    required this.media,
    required this.categories,
    this.favouriteId,
    this.distance,
  });

  ActivityDetails.fromJson(Map<String, dynamic> json) : super.fromJson(json) {
    specialCases = List<Selection>.from(
      json["special_cases"].map((x) => Selection.fromJson(x)),
    );
    organizers =
        json["media"]["organizers"] != null
            ? List<ImageModel>.from(
                json["media"]["organizers"].map((x) => ImageModel.fromJson(x)))
            :
        [];
    media =
    List<ImageModel>.from(
      json["media"]["media"].map((x) => ImageModel.fromJson(x)),
    );
    categories =
        json['parent_categories']
            .map<Category>((e) => Category.fromJson(e))
            .toList();
    periodic =
        json['periodic_day'] != null
            ? MainPeriodicModel.parseTypes(json['periodic_day'])
            : null;
    distance = json['distance'] != null ? json['distance'] * 1000.0 : null;
    favouriteId = json['favorite_by_me'];
    ticketExpirationDuration = json['expiration_of_ticket'];
  }

  @override
  Map<String, dynamic> toJson() {
    Map<String, dynamic> json = super.toJson();
    json.addAll({
      "special_cases": List<dynamic>.from(specialCases.map((x) => x)),
      "organizers": List<dynamic>.from(organizers.map((x) => x)),
      "media": List<dynamic>.from(media.map((x) => x)),
    });
    return json;
  }
}
