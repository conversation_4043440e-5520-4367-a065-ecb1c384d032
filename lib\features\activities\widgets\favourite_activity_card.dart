import 'package:flutter/material.dart';
import 'package:flutter_rating_bar/flutter_rating_bar.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart';
import 'package:gopal/core/routes.dart';
import 'package:gopal/core/style/repo.dart';
import 'package:gopal/core/widgets/image.dart';
import 'package:gopal/features/widgets/general_componenets/svg_icon.dart';

import '../my_activities/models/favourite_activity.dart';

class FavouriteActivityCard extends StatelessWidget {
  final FavouriteActivity activity;
  final Widget? trailing;
  const FavouriteActivityCard({
    super.key,
    this.trailing,
    required this.activity,
  });

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap:
          () => Nav.to(
            Pages.activity_details,
            arguments: activity.id,
            preventDuplicates: false,
          ),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        decoration: BoxDecoration(
          color: StyleRepo.white,
          boxShadow: StyleRepo.elevation_2,
          borderRadius: BorderRadius.circular(10),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Expanded(
                  child: Text(
                    activity.name,
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                    style: context.textTheme.bodyLarge!.copyWith(
                      color: StyleRepo.blueViolet,
                      fontWeight: FontWeight.w700,
                    ),
                  ),
                ),
                if (trailing != null) const Gap(12),
                if (trailing != null) trailing!,
              ],
            ),
            Divider(color: StyleRepo.grey.shade200),
            Text(
              activity.description,
              style: context.textTheme.bodySmall,
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
            Divider(color: StyleRepo.grey.shade200),
            Row(
              children: [
                AppImage(
                  path: activity.center.profileImage.small,
                  type: ImageType.CachedNetwork,
                  height: 40,
                  width: 40,
                  decoration: const BoxDecoration(shape: BoxShape.circle),
                ),
                const Gap(12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        activity.center.name,
                        style: context.textTheme.bodyLarge!.copyWith(
                          color: StyleRepo.blueViolet,
                          fontWeight: FontWeight.w700,
                        ),
                      ),
                      RatingBar.builder(
                        itemSize: 20,
                        unratedColor: Colors.transparent,
                        allowHalfRating: true,
                        ignoreGestures: true,
                        initialRating: activity.center.rate,
                        itemBuilder: (_, __) {
                          return SvgIcon(
                            Assets.icons.star.path,
                            color: StyleRepo.yellow.shade600,
                          );
                        },
                        onRatingUpdate: (_) {},
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
