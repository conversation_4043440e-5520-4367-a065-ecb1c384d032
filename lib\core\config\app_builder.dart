import 'dart:developer';
import 'dart:io';

import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_device_id/flutter_device_id.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:gopal/core/localization/localization.dart';
import 'package:gopal/core/services/register_fcm/service.dart';
import 'package:gopal/core/services/rest_api/constants/end_points.dart';
import 'package:gopal/core/services/rest_api/models/request.dart';
import 'package:gopal/core/services/share/deep_link.dart';
import 'package:gopal/core/widgets/loading.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_crashlytics/firebase_crashlytics.dart';
import 'package:flutter/foundation.dart';
import 'package:timeago/timeago.dart' as timeago;

import '../constants/storages_names.dart';
import '../models/user/main_user.dart';
import '../routes.dart';
import '../services/firebase_messaging/firebase_messaging.dart';
import '../services/local_notifications/local_notification.dart';
import '../services/rest_api/api_service.dart';
import '../style/themes.dart';
import '../services/firebase_messaging/constants/firebase_options.dart';
import 'defaults.dart';
import 'role.dart';

class AppBuilder extends GetxService {
  bool isDev;
  AppBuilder({this.isDev = false});

  //SECTION - Data stored in storage
  Role role = Default.defaultRole;
  String? token;
  int? filteredCityId;
  ValueNotifier<MainUser?> user = ValueNotifier(null);
  //!SECTION

  //SECTION region- Storages boxes

  GetStorage box = GetStorage(StoragesNames.app);
  final secureStorage = const FlutterSecureStorage();

  //SECTION - Load Data

  loadUserData() async {
    await box.initStorage;

    if (!box.hasData("theme")) {
      setTheme(Default.defaultTheme);
    } else {
      updateTheme(AppTheme.fromString(box.read("theme")));
    }

    if (box.hasData("role")) {
      role = Role.fromString(box.read("role"));
    }

    if (box.hasData("filtered_city_id")) {
      filteredCityId = box.read("filtered_city_id");
    }

    if (box.hasData("user")) {
      user.value = MainUser.fromStorage(box.read("user"));
    }

    try {
      Map<String, dynamic> data = {};
      data = await secureStorage.readAll();
      if (data.keys.contains("token")) {
        token = data["token"];
      }
    } catch (_) {
      if (role is User) {
        setRole(const UnregisteredUser());
      }
    }
    logData();
  }

  logData() {
    log('role: $role', name: "APP BUILDER");
    log('token: $token', name: "APP BUILDER");
    log('filtered city id: $filteredCityId', name: "APP BUILDER");
    log('user: $user', name: "APP BUILDER");
  }

  //!SECTION

  //SECTION - Set Data

  ///Used to set user data when login or register
  setUserData({
    required Role role,
    required String token,
    required MainUser user,
  }) {
    setRole(role);
    setToken(token);
    setUser(user);
  }

  setTheme(AppTheme theme) {
    box.write("theme", theme.name);
    this.theme = theme;
  }

  ///Change user role and store in storage
  setRole(Role role) {
    box.write("role", role.toString());
    this.role = role;
  }

  setUser(MainUser? user) {
    if (user == null) {
      box.remove('user');
      this.user.value = null;
      return;
    }
    box.write("user", user.toStorage());
    this.user.value = user;
  }

  setToken(String? token) {
    Get.find<APIService>().setToken(token);
    if (token == null) {
      secureStorage.delete(key: "token");
      this.token = null;
      return;
    }
    secureStorage.write(key: "token", value: token);
    this.token = token;
  }

  setfilteredCity(int? cityId) {
    Get.find<APIService>().setFilteredCity(cityId);
    if (cityId == null) {
      box.remove('filtered_city_id');
      filteredCityId = null;
      return;
    }
    box.write('filtered_city_id', cityId);
    filteredCityId = cityId;
  }

  ///Used to reset user data and clean up the storage
  logout({bool isDelete = false}) async {
    Loading.show();
    await APIService.instance.requestAPI(
      Request(
        endPoint: isDelete ? EndPoints.delete_account : EndPoints.logout,
        method: RequestMethod.Post,
      ),
    );
    setRole(const UnregisteredUser());
    setToken(null);
    setUser(null);
    setfilteredCity(null);
    await FCMRegisterService.disopse();
    await FirebaseMessagingService.deleteToken();
    Loading.dispose();
    Nav.offUntil(role.landing, (_) => false);
  }

  //!SECTION
  //!SECTION

  //SECTION - Localization
  changeLocale({BuildContext? context, required AppLocalization locale}) {
    (context ?? Get.context!).setLocale(locale.locale);
    Get.updateLocale(locale.locale);
    Get.find<APIService>().setLanguage(locale.value);
    FCMRegisterService.disopse().then((_) => FCMRegisterService.init());
  }

  updateLocale() => changeLocale(
    locale: currentLocale.isEnglish ? AppLocalization.ar : AppLocalization.en,
  );

  AppLocalization get currentLocale => AppLocalization.fromString(
    EasyLocalization.of(Get.context!)!.currentLocale!.languageCode,
  );
  //!SECTION

  //SECTION - Theme
  final Rx<AppTheme> _theme = Default.defaultTheme.obs;
  AppTheme get theme => _theme.value;
  set theme(AppTheme value) => _theme.value = value;

  updateTheme(AppTheme theme) {
    // ThemeSwitcher.of(context).changeTheme(theme: theme.theme);
    setTheme(theme);
  }
  //!SECTION

  //SECTION - Initialize app

  ///Initializer for the application
  ///you have to do navigation here
  Future<Function?> init() async {
    try {
      FirebaseApp app = await Firebase.initializeApp(
        options: DefaultFirebaseOptions.currentPlatform,
      );

      log('Initialized default app $app');

      if (!kDebugMode) {
        try {
          FlutterError.onError = (details) {
            FirebaseCrashlytics.instance.recordFlutterError(details);
          };
        } catch (_) {}
        PlatformDispatcher.instance.onError = (error, stack) {
          FirebaseCrashlytics.instance.recordFlutterError(
            FlutterErrorDetails(exception: error, stack: stack),
          );
          return true;
        };
      }
    } catch (_) {
      log("Failed to init firebase");
    }

    try {
      timeago.setLocaleMessages('ar', timeago.ArMessages());
      timeago.setLocaleMessages('ar_short', timeago.ArShortMessages());
    } catch (_) {}

    await loadUserData();

    Get.put(
      APIService(
        token: token,
        language: currentLocale.value,
        cityId: filteredCityId,
        headers: {
          "Accept": "application/json",
          "Request-Source": "MOBILE",
          "Platform":
              Platform.isAndroid
                  ? "Android"
                  : Platform.isIOS
                  ? "IOS"
                  : "Other",
          "Accept-Version": (await PackageInfo.fromPlatform()).version,
          "device-id": await FlutterDeviceId().getDeviceId() ?? "",
        },
        withLog: false,
      ),
    );

    bool willFirebaseDoNavigation = await FirebaseMessagingService.init();
    await FirebaseMessagingService.getToken();
    bool willLocalNotificationDoNavigation =
        await LocalNotificationService.init();

    bool willDeepLinkDoNavigation = await DeepLinks.initialize();

    bool willDoNavigation =
        willFirebaseDoNavigation ||
        willLocalNotificationDoNavigation ||
        willDeepLinkDoNavigation;

    await role.initialize();

    if (willDoNavigation) return null;
    return () => Nav.offUntil(role.landing, (_) => false);
  }

  //!SECTION
}
