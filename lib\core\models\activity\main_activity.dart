import 'package:gopal/core/models/image.dart';

class MainActivity {
  int id;
  String name;
  String description;
  ImageModel cover;
  ActivityCenter center;
  int booksCount;
  int? subActivityId;
  bool isAvailable;

  MainActivity({
    required this.id,
    required this.name,
    required this.description,
    required this.cover,
    required this.center,
    required this.booksCount,
    required this.isAvailable,
    this.subActivityId,
  });

  factory MainActivity.fromJson(Map<String, dynamic> json) {
    return MainActivity(
      id: json["id"],
      name: json["name"] ?? "",
      description: json["description"] ?? "",
      cover: ImageModel.tryParse(
        json['media'] is List ? {} : json['media']['main_image'].first,
      ),

      center: ActivityCenter.fromJson(json["provider"]),
      isAvailable: json['is_available'] == 1,
      booksCount: json['book_transaction_count'] ?? 0,
      subActivityId: json['sub_activities']?['id'],
    );
  }

  Map<String, dynamic> toJson() => {
    "id": id,
    "main_activity_name": name,
    "main_activity_description": description,
    "media": cover,
    "center": center,
  };
}

class ActivityCenter {
  late int id;
  late bool isTrusted;
  late String name;
  late double rate;
  late ImageModel profileImage;

  ActivityCenter({
    required this.id,
    required this.isTrusted,
    required this.name,
    required this.rate,
    required this.profileImage,
  });

  ActivityCenter.fromJson(Map<String, dynamic> json) {
    id = json["id"];
    isTrusted = json["is_trusted"] == 1;
    name = json["name"];
    rate = (json["rate"] ?? 0) * 1.0;
    try {
      profileImage = ImageModel.fromJson(
        json['user']['media']['profile'].first,
      );
    } catch (_) {
      profileImage = ImageModel.empty();
    }
  }

  Map<String, dynamic> toJson() => {
    "id": id,
    "is_trusted": isTrusted ? 1 : 0,
    "name": name,
    "rate": rate,
  };
}
