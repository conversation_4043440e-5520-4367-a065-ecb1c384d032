import 'package:easy_localization/easy_localization.dart';
import 'package:gopal/core/localization/strings.dart';

extension NumExtension on num {
  String get approximate {
    if (this < 1000) {
      return '$this';
    } else if (this < 1000000) {
      return '${this ~/ 1000} K';
    } else {
      return '${this ~/ 1000000} M';
    }
  }

  String get approximateDistance {
    if (this < 1000) {
      return LocaleKeys.distance_m_from_your_area.plural(toInt());
    } else {
      final recent = this % 1000;
      final value =
          "${(this ~/ 1000)}${recent > 100 ? ".${recent ~/ 100}" : ""}";
      return LocaleKeys.distance_km_from_your_area.tr(args: [value]);
    }
  }

  String get priceRound {
    if (this % 1 == 0) {
      return toStringAsFixed(0);
    }
    return toStringAsFixed(1);
  }
}

extension IntExtension on int {
  String get formatSeconds {
    int minutes = (this / 60).floor();
    int remainingSeconds = this % 60;

    String formattedMinutes = minutes.toString().padLeft(2, '0');
    String formattedSeconds = remainingSeconds.toString().padLeft(2, '0');

    return '$formattedMinutes:$formattedSeconds';
  }
}
