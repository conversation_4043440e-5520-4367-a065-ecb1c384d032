import 'package:get/get.dart';
import 'package:gopal/core/services/state_management/observable_variable.dart';

import '../../../core/services/rest_api/rest_api.dart';

class HelpPageController extends GetxController {
  ObsVar<String> help = ObsVar(null);

  fetchHelp() async {
    ResponseModel response = await APIService.instance.requestAPI(
      Request(
        endPoint: EndPoints.settings,
        params: {"type": "HELP_AND_SUPPORT_TEXT"},
        fromJson: (json) => json['text'],
      ),
    );
    if (response.success) {
      help.value = response.data.first;
    } else {
      help.error = response.message;
    }
  }

  @override
  void onInit() {
    fetchHelp();
    super.onInit();
  }
}
