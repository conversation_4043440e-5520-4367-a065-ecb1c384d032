import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart' hide Trans;
import 'package:gopal/core/localization/strings.dart';
import 'package:gopal/features/profile/create_child/widgets/form.dart';
import 'package:gopal/features/widgets/general_componenets/buttons/elevated_button.dart';

import '../../widgets/general_componenets/pages/rounded_page.dart';
import 'controller.dart';
import 'widgets/medical_info.dart';

class CreateChildPage extends GetView<CreateChildPageController> {
  const CreateChildPage({super.key});

  @override
  Widget build(BuildContext context) {
    return RoundedPage(
      title: Text(
        controller.editMode
            ? LocaleKeys.edit_child.tr()
            : LocaleKeys.add_child.tr(),
      ),
      child: ListView(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        children: [
          const Gap(10),
          const ChildCreationForm(),
          const Gap(20),
          const MedicalInfoFields(),
          const Gap(20),
          AppElevatedButton(
            onPressed: () async => await controller.confirm(),
            child: Text(LocaleKeys.confirm.tr()),
          ),
          const Gap(20),
        ],
      ),
    );
  }
}
