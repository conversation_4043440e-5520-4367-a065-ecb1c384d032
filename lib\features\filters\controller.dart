import 'dart:async';
import 'dart:math';
import 'dart:developer' as dev;

import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart' hide Trans;
import 'package:gopal/core/localization/strings.dart';
import 'package:gopal/core/models/constants/gender.dart';
import 'package:gopal/core/services/state_management/observable_variable.dart';
import 'package:gopal/core/services/state_management/constants/variable_status.dart';
import 'package:gopal/features/usecases/gender/choose_gender.dart';
import 'package:multi_dropdown/multiselect_dropdown.dart';

import '../../core/models/selection.dart';
import '../../core/services/rest_api/rest_api.dart';
import 'models/categories.dart/main_category.dart';
import 'models/filters_data.dart';
import 'models/price_type.dart';

class FiltersPageController extends GetxController {
  final int? initialCategoryId;
  FiltersPageController({this.initialCategoryId});
  FiltersData prevData = FiltersData();

  final Rx<CategoryFilter> _selectedCategory = CategoryFilter.initialValue.obs;
  CategoryFilter get selectedCategory => _selectedCategory.value;
  set selectedCategory(CategoryFilter value) {
    if (value.id == 0) {
      subcategoriesController.setSelectedOptions([]);
    }
    if (value.id != 0 && value != _selectedCategory.value) {
      loadSubcategories(value.id);
    }
    _selectedCategory.value = value;
    calculatePricentage();
  }

  MultiSelectController<CategoryFilter> subcategoriesController =
      MultiSelectController<CategoryFilter>();
  StreamController<List<CategoryFilter>> subcategoriesStream =
      StreamController();

  //SECTION - Calculate Price Range
  final Rx<RangeValues> _priceRange = const RangeValues(0, 0).obs;
  RangeValues get priceRange => _priceRange.value;
  set priceRange(RangeValues value) => _priceRange.value = value;

  final Rx<RangeValues> _minMaxPriceRange = const RangeValues(0, 0).obs;
  RangeValues get minMaxPriceRange => _minMaxPriceRange.value;
  set minMaxPriceRange(RangeValues value) => _minMaxPriceRange.value = value;

  calculatePricentage() {
    if (selectedPriceType == PriceType.any) {
      priceRange = const RangeValues(0, 0);
      minMaxPriceRange = priceRange;
      return;
    }
    if (selectedCategory == CategoryFilter.initialValue) {
      if (selectedPriceType == PriceType.per_course) {
        priceRange = RangeValues(
          categories
              .map(
                (element) => element.coursePriceRange?.start ?? double.infinity,
              )
              .reduce(min),
          categories
              .map((element) => element.coursePriceRange?.end ?? 0)
              .reduce(max),
        );
      } else {
        priceRange = RangeValues(
          categories
              .map(
                (element) => element.hourPriceRange?.start ?? double.infinity,
              )
              .reduce(min),
          categories
              .map((element) => element.hourPriceRange?.end ?? 0)
              .reduce(max),
        );
      }
    } else {
      if (subcategoriesController.selectedOptions.isEmpty) {
        priceRange =
            (selectedPriceType == PriceType.per_course
                ? selectedCategory.coursePriceRange
                : selectedCategory.hourPriceRange) ??
            const RangeValues(0, 0);
      } else {
        if (selectedPriceType == PriceType.per_course) {
          priceRange = RangeValues(
            subcategoriesController.selectedOptions
                .map((e) => e.value?.coursePriceRange?.start ?? double.infinity)
                .reduce(min),
            subcategoriesController.selectedOptions
                .map((e) => e.value?.coursePriceRange?.end ?? 0)
                .reduce(max),
          );
        } else {
          priceRange = RangeValues(
            subcategoriesController.selectedOptions
                .map((e) => e.value?.hourPriceRange?.start ?? double.infinity)
                .reduce(min),
            subcategoriesController.selectedOptions
                .map((e) => e.value?.hourPriceRange?.end ?? 0)
                .reduce(max),
          );
        }
      }
    }
    if (priceRange.start == double.infinity ||
        priceRange.end == double.infinity) {
      priceRange = const RangeValues(0, 0);
    }
    minMaxPriceRange = priceRange;
    dev.log("$minMaxPriceRange", name: "PRICE RANGE");
  }
  //!SECTION

  final Rx<PriceType> _selectedPriceType = PriceType.any.obs;
  PriceType get selectedPriceType => _selectedPriceType.value;
  set selectedPriceType(PriceType value) {
    _selectedPriceType.value = value;
    calculatePricentage();
  }

  final Rx<int> _rating = 0.obs;
  int get rating => _rating.value;
  set rating(int value) => _rating.value = value;

  final Rx<int> _minAge = 0.obs;
  int get minAge => _minAge.value;
  set minAge(int value) => _minAge.value = value;

  final Rx<int> _maxAge = 0.obs;
  int get maxAge => _maxAge.value;
  set maxAge(int value) => _maxAge.value = value;

  ObsVar<Gender> gender = ObsVar(null);
  Future<Gender?> pickGender() async {
    Gender? pickedGender = await ChooseGender.show(
      initialValue: gender.value,
      title: LocaleKeys.please_select_child_gender.tr(),
      isSingleSelect: false,
    );
    if (pickedGender != null) {
      gender.value = pickedGender;
    }
    return pickedGender;
  }

  ObsVar<Selection> specialNeed = ObsVar(null);

  applyFilters() {
    prevData = FiltersData(
      category: selectedCategory.id == 0 ? null : selectedCategory,
      subcategories:
          subcategoriesController.selectedOptions.map((e) => e.value!).toList(),
      priceRange:
          (priceRange.start == 0 && priceRange.end == 0) ? null : priceRange,
      rating: rating == 0 ? null : rating,
      minAge: minAge == 0 ? null : minAge,
      maxAge: maxAge == 0 ? null : maxAge,
      specialNeed: specialNeed.value,
      priceType: selectedPriceType,
      gender: gender.value,
    );

    Get.back(result: prevData);
  }

  resetFilters() {
    selectedCategory = CategoryFilter.initialValue;
    subcategoriesController.setSelectedOptions([]);
    minMaxPriceRange = const RangeValues(0, 0);
    priceRange = const RangeValues(0, 0);
    rating = 0;
    minAge = 0;
    maxAge = 0;
    gender.reset();
    specialNeed.reset();
    selectedPriceType = PriceType.any;
  }

  ObsList<CategoryFilter> categories = ObsList([]);
  loadCategories() async {
    ResponseModel response = await APIService.instance.requestAPI(
      Request(endPoint: EndPoints.categories, params: {"with_price_range": 1}),
    );
    if (response.success) {
      categories.value = [
        CategoryFilter.initialValue,
        ...response.data
            .map<CategoryFilter>((json) => CategoryFilter.fromJson(json))
            .toList(),
      ];
      if (initialCategoryId != null) {
        selectedCategory = categories.firstWhere(
          (element) => element.id == initialCategoryId,
        );
      }
      calculatePricentage();
    } else {
      categories.error = response.message;
    }
  }

  ObsList<CategoryFilter> subcategories = ObsList([]);

  loadSubcategories(int categoryId) async {
    subcategories.reset();
    subcategoriesController.setSelectedOptions([]);
    ResponseModel response = await APIService.instance.requestAPI(
      Request(
        endPoint: EndPoints.subcategories(categoryId),
        params: {"with_price_range": 1},
      ),
    );
    if (response.success) {
      if (response.data.isEmpty) {
        subcategories.status = VariableStatus.HasData;
      } else {
        subcategoriesStream = StreamController();
        subcategories.value =
            response.data
                .map<CategoryFilter>((json) => CategoryFilter.fromJson(json))
                .toList();
        subcategoriesController.setOptions(
          subcategories
              .map(
                (e) => ValueItem<CategoryFilter>(
                  label: e.name.toString(),
                  value: e,
                ),
              )
              .toList(),
        );
      }
    } else {
      subcategories.error = response.message;
    }
  }

  ObsList<Selection> specialCases = ObsList([]);
  fetchSpecialCases() async {
    ResponseModel response = await APIService.instance.requestAPI(
      Request<Selection>(
        endPoint: EndPoints.special_cases,
        fromJson: Selection.fromJson,
      ),
    );
    if (response.success) {
      specialCases.value = response.data;
    } else {
      specialCases.error = response.message;
    }
  }

  refreshSpecialCases() async {
    specialCases.reset();
    await fetchSpecialCases();
  }

  @override
  void onInit() {
    loadCategories();
    fetchSpecialCases();
    subcategoriesController.addListener(() {
      subcategoriesStream.add(
        subcategoriesController.selectedOptions.map((e) => e.value!).toList(),
      );
    });
    super.onInit();
  }
}
