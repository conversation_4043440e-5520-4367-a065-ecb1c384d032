import 'dart:convert';
import 'dart:developer';
import 'dart:math' as math;

import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:gopal/core/models/notifications/notification.dart';
import 'package:gopal/core/services/notifications_counter/counter.dart';

import '../../routes.dart';
import '../local_notifications/local_notification.dart';
import 'notifications_actions.dart' as notification_action;
import '../notifications_click/notification_click.dart' as notification_click;

@pragma("vm:entry-point")
class FirebaseMessagingService {
  static late FirebaseMessaging firebaseMessaging;

  ///Return [true] if a firebase notification has opened the app and the init function will do the navigation
  ///Return [false] if there is no navigatoin will handled in this function
  static Future<bool> init() async {
    try {
      firebaseMessaging = FirebaseMessaging.instance;
    } catch (_) {
      return false;
    }
    RemoteMessage? messageOpenedApp =
        await firebaseMessaging.getInitialMessage();

    try {
      NotificationSettings settings = await firebaseMessaging.requestPermission(
        alert: true,
        announcement: false,
        badge: true,
        carPlay: false,
        criticalAlert: false,
        provisional: false,
        sound: true,
      );
      log('User granted permission: ${settings.authorizationStatus}');
    } catch (_) {}

    initListeners();

    if (messageOpenedApp != null) {
      (Pages, dynamic, bool)? result = await notification_click
          .notificationClick(NotificationModel.fromJson(messageOpenedApp.data));
      if (result != null) {
        Nav.offUntil(result.$1, (_) => false, arguments: result.$2);
        return true;
      }
    }
    return false;
  }

  static void initListeners() async {
    FirebaseMessaging.onMessage.listen(getNotification);
    FirebaseMessaging.onMessageOpenedApp.listen((event) async {
      log(event.data.toString());
      (Pages, dynamic, bool)? result = await notification_click
          .notificationClick(NotificationModel.fromJson(event.data));
      log(result.toString());
      if (result != null) {
        Nav.to(result.$1, arguments: result.$2, preventDuplicates: result.$3);
      }
    });
    // FirebaseMessaging.onBackgroundMessage(firebaseMessagingBackgroundHandler);
    firebaseMessaging.setForegroundNotificationPresentationOptions(
      alert: true,
      badge: true,
      sound: true,
    );
  }

  @pragma("vm:entry-point")
  static getNotification(
    RemoteMessage message, {
    bool isBackground = false,
  }) async {
    log("${message.toMap()}", name: "FIREBASE");
    log("${message.data['id'].runtimeType}", name: "FIREBASE");

    NotificationsCounter.notificationsCount++;

    Map<String, dynamic> data = message.data;

    bool showNotification = await notification_action.recieveNotificationAction(
      NotificationModel.fromJson(data),
    );
    if (!showNotification) return;

    LocalNotificationService.showNotification(
      id: math.Random().nextInt(5000),
      title: message.notification?.title ?? "No title sent",
      subTitle: message.notification?.body ?? "No body sent",
      payload: jsonEncode(data),
    );
  }

  static Future<String?> getToken() async {
    String? token;
    try {
      token = await firebaseMessaging.getToken();
    } catch (e) {
      log(e.toString());
    }
    log('FCM_TOKEN: $token', name: "FIREBASE");
    return token;
  }

  static Future<bool> deleteToken() async {
    try {
      await firebaseMessaging.deleteToken();
      return true;
    } catch (_) {
      return false;
    }
  }

  static Future<String?> regenerateToken() async {
    try {
      await firebaseMessaging.deleteToken();
    } catch (_) {
      return null;
    }
    return await getToken();
  }

  static Future<bool> subscribeToTopic(String topic) async {
    try {
      await firebaseMessaging.subscribeToTopic(topic);
      log("Subscribed to $topic", name: "FIREBASE");
    } catch (_) {
      return false;
    }
    return true;
  }

  static Future<bool> unsubscribeFromTopic(String topic) async {
    try {
      await firebaseMessaging.unsubscribeFromTopic(topic);
      log("Unubscribed from $topic", name: "FIREBASE");
    } catch (_) {
      return false;
    }
    return true;
  }
}
