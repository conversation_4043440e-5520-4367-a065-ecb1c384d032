class PointsTransaction {
  int id;
  int points;
  DateTime expirationAt;
  bool isUsed;
  String status;
  DateTime createdAt;

  PointsTransaction({
    required this.id,
    required this.points,
    required this.expirationAt,
    required this.isUsed,
    required this.status,
    required this.createdAt,
  });

  factory PointsTransaction.fromJson(Map<String, dynamic> json) =>
      PointsTransaction(
        id: json["id"],
        points: (json["points"] as int).abs(),
        expirationAt: DateTime.parse(json["expiration_at"]),
        isUsed: json['reason'] == "USED",
        status: json["status"],
        createdAt: DateTime.parse(json["created_at"]),
      );

  Map<String, dynamic> toJson() => {
    "id": id,
    "points": points,
    "expiration_at": expirationAt.toIso8601String(),
    "status": status,
    "created_at": createdAt.toIso8601String(),
  };
}
