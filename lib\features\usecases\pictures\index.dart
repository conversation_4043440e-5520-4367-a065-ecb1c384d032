import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:gopal/core/demo/media.dart';
import 'package:gopal/core/style/repo.dart';
import 'package:gopal/core/widgets/image.dart';
import 'package:gopal/features/usecases/players/video/index.dart';
import 'package:gopal/features/widgets/general_componenets/svg_icon.dart';

class MediaPreview extends StatelessWidget {
  final List<String> media;
  final int initialIndex;
  const MediaPreview({super.key, required this.media, this.initialIndex = 0});

  static preview({required List<String> media, int initialIndex = 0}) =>
      Get.dialog(
        MediaPreview(
          media: media,
          initialIndex: initialIndex,
        ),
        barrierColor: Colors.transparent,
      );

  @override
  Widget build(BuildContext context) {
    return Dismissible(
      key: GlobalKey(),
      direction: DismissDirection.vertical,
      onDismissed: (_) => Get.back(),
      child: Container(
        decoration: const BoxDecoration(
          color: Colors.black,
        ),
        child: PageView.builder(
          controller:
              PageController(initialPage: initialIndex, viewportFraction: 1.1),
          itemCount: media.length,
          itemBuilder: (context, index) {
            return Padding(
              padding: EdgeInsets.symmetric(horizontal: Get.width * .05),
              child: media[index].isVideoFileName
                  ? _VideoView(link: media[index])
                  : AppImage(
                      path: media[index],
                      type: ImageType.CachedNetwork,
                      width: double.infinity,
                      fit: BoxFit.contain,
                    ),
            );
          },
        ),
      ),
    );
  }
}

class _VideoView extends StatelessWidget {
  final String link;
  const _VideoView({required this.link});

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () => Get.to(() => VideoPlayerWidget(video: link)),
      child: Center(
        child: Stack(
          alignment: Alignment.center,
          children: [
            AppImage(
              path: DemoMedia.getRandomImage,
              type: ImageType.CachedNetwork,
              width: double.infinity,
              fit: BoxFit.contain,
            ),
            Container(
              decoration: const BoxDecoration(
                color: Colors.black38,
              ),
              child: Center(
                child: Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    border: Border.all(color: StyleRepo.white),
                  ),
                  child: SvgIcon(
                    Assets.icons.play.path,
                    color: StyleRepo.white,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
