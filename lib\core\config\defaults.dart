import 'package:flutter/services.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';

import '../localization/localization.dart';
import '../style/themes.dart';
import 'role.dart';

abstract class Default {
  static const AppLocalization defaultLocale = AppLocalization.en;

  static const Role defaultRole = FirstOpenUser();

  static const String appTitle = 'gopal';

  static const testNumber = "0514725836";

  static const AppTheme defaultTheme = AppTheme.light;

  static const LatLng defaultLocation = LatLng(24.774265, 46.738586);

  static preferredOrientation() {
    SystemChrome.setPreferredOrientations([
      DeviceOrientation.portraitUp,
      DeviceOrientation.portraitDown,
    ]);
  }

  static const landing = "https://gopalsa.com/";
}
