import 'package:google_maps_flutter/google_maps_flutter.dart';

class CenterLocation {
  int id;
  String name;
  LatLng location;

  CenterLocation({
    required this.id,
    required this.name,
    required this.location,
  });

  factory CenterLocation.fromJson(Map<String, dynamic> json) => CenterLocation(
    id: json['provider_id'],
    name: json['provider_name'],
    location: LatLng(json['lat'] * 1.0, json['lng'] * 1.0),
  );
}
