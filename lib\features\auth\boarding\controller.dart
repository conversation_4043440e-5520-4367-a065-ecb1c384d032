import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:gopal/core/config/app_builder.dart';
import 'package:gopal/core/config/role.dart';
import 'package:gopal/core/routes.dart';

import 'models/boarding_data.dart';

class BoardingPageController extends GetxController {
  AppBuilder appBuilder = Get.find<AppBuilder>();

  late PageController pageController;

  final Rx<int> _currentPage = 0.obs;
  int get currentPage => _currentPage.value;
  set currentPage(int value) => _currentPage.value = value;

  @override
  void onInit() {
    pageController = PageController();
    super.onInit();
  }

  @override
  void onClose() {
    pageController.dispose();
    super.onClose();
  }

  next() async {
    if (currentPage == BoardingData.boarding.length - 1) {
      appBuilder.setRole(const NewGuestUser());
      await appBuilder.role.initialize();
      Nav.offUntil(appBuilder.role.landing, (_) => false);
    } else {
      pageController.animateToPage(
        currentPage + 1,
        duration: 300.milliseconds,
        curve: Curves.linear,
      );
    }
  }
}
