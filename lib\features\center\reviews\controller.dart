import 'package:dio/dio.dart';
import 'package:get/get.dart';

import '../../../core/services/rest_api/rest_api.dart';

class CenterReviewsPageController extends GetxController {
  final int id;
  CenterReviewsPageController(this.id);

  Future<ResponseModel> fetchData(int page, CancelToken cancel) {
    return APIService.instance.requestAPI(
      Request(
        endPoint: EndPoints.center_reviews,
        params: {
          "page": page,
          "reviewable_type": "PROVIDER",
          "reviewable_id": id,
        },
        cancelToken: cancel,
      ),
    );
  }
}
