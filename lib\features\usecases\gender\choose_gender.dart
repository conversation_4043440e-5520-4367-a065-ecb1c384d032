import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart' hide Trans;
import 'package:gopal/core/localization/strings.dart';
import 'package:gopal/core/models/constants/gender.dart';
import 'package:gopal/core/services/state_management/observable_variable.dart';
import 'package:gopal/core/style/assets/gen/assets.gen.dart';
import 'package:gopal/core/style/repo.dart';
import 'package:gopal/features/widgets/general_componenets/buttons/elevated_button.dart';

import 'widgets/gender_card.dart';

class ChooseGender extends StatelessWidget {
  static Future<Gender?> show({
    Gender? initialValue,
    String? title,
    bool? isSingleSelect,
  }) async => await Get.dialog(
    ChooseGender(
      initialValue: initialValue,
      title: title,
      isSingleSelect: isSingleSelect ?? true,
    ),
  );

  ChooseGender({
    super.key,
    Gender? initialValue,
    this.title,
    required this.isSingleSelect,
  }) {
    gender = ObsVar(initialValue);
  }
  final String? title;
  final bool isSingleSelect;

  late final ObsVar<Gender> gender;

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Padding(
        padding: EdgeInsets.symmetric(horizontal: Get.width * .1),
        child: Material(
          color: Colors.transparent,
          child: Stack(
            alignment: Alignment.topCenter,
            children: [
              Container(
                padding: const EdgeInsets.fromLTRB(16, 40, 16, 16),
                margin: const EdgeInsets.only(top: 50),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(35),
                  color: StyleRepo.white,
                ),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    if (title != null)
                      Text(
                        title!,
                        style: context.textTheme.bodyMedium!.copyWith(
                          color: StyleRepo.grey.shade700,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    if (title != null) const Gap(12),
                    Row(
                      children: [
                        Expanded(
                          child: GestureDetector(
                            onTap: () {
                              if (!isSingleSelect) {
                                if (gender.value == Gender.male) {
                                  gender.value = Gender.both;
                                  return;
                                } else if (gender.value == Gender.both) {
                                  gender.value = Gender.male;
                                  return;
                                }
                              }
                              gender.value = Gender.female;
                            },
                            child: Obx(() {
                              gender.status;
                              return GenderCard(
                                Gender.female,
                                isSelected:
                                    gender.value == Gender.female ||
                                    gender.value == Gender.both,
                              );
                            }),
                          ),
                        ),
                        const Gap(24),
                        Expanded(
                          child: GestureDetector(
                            onTap: () {
                              if (!isSingleSelect) {
                                if (gender.value == Gender.female) {
                                  gender.value = Gender.both;
                                  return;
                                } else if (gender.value == Gender.both) {
                                  gender.value = Gender.female;
                                  return;
                                }
                              }
                              gender.value = Gender.male;
                            },
                            child: Obx(() {
                              gender.status;
                              return GenderCard(
                                Gender.male,
                                isSelected:
                                    gender.value == Gender.male ||
                                    gender.value == Gender.both,
                              );
                            }),
                          ),
                        ),
                      ],
                    ),
                    const Gap(16),
                    Obx(
                      () => AppElevatedButton(
                        onPressed:
                            gender.hasData
                                ? () => Get.back(result: gender.value)
                                : null,
                        child: Text(LocaleKeys.confirm.tr()),
                      ),
                    ),
                  ],
                ),
              ),
              Assets.icons.genderSticker.svg(),
            ],
          ),
        ),
      ),
    );
  }
}
