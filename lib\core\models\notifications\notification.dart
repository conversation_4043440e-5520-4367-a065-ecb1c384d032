import 'dart:convert';
import 'dart:math';

import 'data/main_notification_data.dart';
import 'type.dart';

class NotificationModel {
  late int id;
  late NotificationType type;
  late String title;
  late String body;
  late DateTime date;
  late bool seen;
  MainNotificationData? data;

  NotificationModel.fromJson(Map<String, dynamic> json) {
    id = json['id'] ?? Random().nextInt(9999999);
    title = json['title'] ?? "No Title";
    body = json['body'] ?? "No Body";
    date =
        DateTime.tryParse(json['created_at'] ?? "")?.toLocal() ??
        DateTime.now();
    seen = json['seen'] == 1;
    type = NotificationType.fromString(json['type']);
    Map<String, dynamic>? extra =
        (json['extra'] is String) ? jsonDecode(json['extra']) : json['extra'];
    switch (type) {
      case NotificationType.activity:
        data = ActivityNotificationData.fromJson(extra!);
        break;
      case NotificationType.suggestion:
        data = SuggestionsActivitiesNotificationData.fromJson(extra!);
        break;
      case NotificationType.payment_success:
        //TODO - nullable
        data = PaymentSuccessNotificationData.fromJson(extra ?? {});
        break;
      case NotificationType.confirm_book:
        data = BookAcceptedNotificationData.fromJson(extra ?? {});
        break;
      case NotificationType.new_message:
        data = NewMessageNotificationData.fromJson(extra!);
        break;
      default:
        data = null;
    }
  }
}
