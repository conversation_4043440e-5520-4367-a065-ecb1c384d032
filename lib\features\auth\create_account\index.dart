import 'package:easy_localization/easy_localization.dart' hide TextDirection;
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart' hide Trans;
import 'package:gopal/core/localization/strings.dart';
import 'package:gopal/core/style/assets/gen/assets.gen.dart';
import 'package:gopal/core/style/repo.dart';
import 'package:gopal/features/widgets/backgrounds/auth_bg.dart';
import 'package:gopal/features/widgets/general_componenets/buttons/elevated_button.dart';

import 'controller.dart';
import 'widgets/children.dart';
import 'widgets/form.dart';
import 'widgets/terms_acception.dart';

class CreateAccountPage extends GetView<CreateAccountPageController> {
  const CreateAccountPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: AuthBackground(
        child: SafeArea(
          child: Column(
            children: [
              const Gap(24),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  const BackButton(),
                  Assets.images.logo.image(
                    width: MediaQuery.sizeOf(context).width * .3,
                  ),
                  const SizedBox(width: 50),
                ],
              ),
              const Gap(24),
              Expanded(
                child: ListView(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 16,
                    vertical: 12,
                  ),
                  children: [
                    Text(
                      LocaleKeys.create_new_account.tr(),
                      style: context.textTheme.headlineSmall!.copyWith(
                        color: StyleRepo.berkeleyBlue,
                      ),
                    ),
                    const Gap(10),
                    Text(
                      LocaleKeys.enter_your_information_to_create_new_account
                          .tr(),
                      style: context.textTheme.bodyMedium,
                    ),
                    const Gap(24),
                    const CreateAccountForm(),
                    const CreateAccountChildren(),
                    const Gap(24),
                    const TermsAcception(),
                    const Gap(24),
                    Obx(
                      () => AppElevatedButton(
                        onTap: () async => await controller.confirm(),
                        enabled: controller.isTermsAccepted,
                        child: Text(LocaleKeys.confirm.tr()),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
