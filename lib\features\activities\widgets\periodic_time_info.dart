import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart' hide Trans;
import 'package:gopal/core/style/repo.dart';
import 'package:gopal/features/widgets/general_componenets/svg_icon.dart';

import '../../../core/localization/strings.dart';
import '../../../core/models/activity/periodic/main_periodic.dart';
import '../../../core/models/activity/periodic/periodic_type.dart';
import '../../../core/utils/date.dart';

class PeriodicTimeInfo extends StatelessWidget {
  final MainPeriodicModel periodic;
  const PeriodicTimeInfo({super.key, required this.periodic});

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            SvgIcon(
              Assets.icons.calendar.path,
              size: 20,
              color: StyleRepo.grey.shade700,
            ),
            const Gap(6),
            Expanded(
              child: RichText(
                text: TextSpan(
                  style: context.textTheme.bodyMedium,
                  children: [
                    TextSpan(
                      text:
                          "${periodic.type == PeriodicType.date ? LocaleKeys.activity_date.tr() : LocaleKeys.activity_duration.tr()}: ",
                      style: const TextStyle(fontWeight: FontWeight.w500),
                    ),
                    TextSpan(
                      text:
                          periodic.type == PeriodicType.date
                              ? "${LocaleKeys.from_to.tr(args: [DateFormat.yMMMd(EasyLocalization.of(context)!.currentLocale!.languageCode).format(periodic.from!), DateFormat.yMMMd(EasyLocalization.of(context)!.currentLocale!.languageCode).format(periodic.to!)])} "
                              : "${LocaleKeys.for_days.plural(periodic.period!, args: ["${periodic.period}"])} ${LocaleKeys.starting_from_the_date_of_joining.tr()} ",
                    ),
                    if (periodic is DailyPeriodic)
                      TextSpan(
                        text: "(${LocaleKeys.daily.tr()})",
                        style: const TextStyle(fontWeight: FontWeight.bold),
                      ),
                    if (periodic is NumberedDaysPeriodic)
                      TextSpan(
                        text:
                            "(${LocaleKeys.days_at_week.plural((periodic as NumberedDaysPeriodic).daysNumber, args: ["${(periodic as NumberedDaysPeriodic).daysNumber}"])})",
                        style: const TextStyle(fontWeight: FontWeight.bold),
                      ),
                    if (periodic is DedicatedDaysPeriodic &&
                        (periodic as DedicatedDaysPeriodic).areHaveTheSameTime)
                      TextSpan(
                        text:
                            "(${(periodic as DedicatedDaysPeriodic).days.map((e) => DateTimeExt.dayName(e.weekDay)).join(", ")})",
                      ),
                  ],
                ),
              ),
            ),
          ],
        ),
        const Gap(12),
        Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            SvgIcon(
              Assets.icons.time.path,
              size: 20,
              color: StyleRepo.grey.shade700,
            ),
            const Gap(6),
            Text(
              "${LocaleKeys.time.tr()}: ",
              style: context.textTheme.bodyMedium!.copyWith(
                fontWeight: FontWeight.w500,
              ),
            ),
            Expanded(
              child: RichText(
                text: TextSpan(
                  style: context.textTheme.bodyMedium,
                  children: [
                    if (periodic is DailyPeriodic)
                      TextSpan(
                        text: LocaleKeys.open_close.tr(
                          args: [
                            (periodic as DailyPeriodic).openAt.format(context),
                            (periodic as DailyPeriodic).closeAt.format(context),
                          ],
                        ),
                      ),
                    if (periodic is NumberedDaysPeriodic)
                      TextSpan(
                        text: LocaleKeys.open_close.tr(
                          args: [
                            (periodic as NumberedDaysPeriodic).openAt.format(
                              context,
                            ),
                            (periodic as NumberedDaysPeriodic).closeAt.format(
                              context,
                            ),
                          ],
                        ),
                      ),
                    if (periodic is DedicatedDaysPeriodic &&
                        (periodic as DedicatedDaysPeriodic).areHaveTheSameTime)
                      TextSpan(
                        text: LocaleKeys.from_to.tr(
                          args: [
                            (periodic as DedicatedDaysPeriodic)
                                .days
                                .first
                                .openAt
                                .format(context),
                            (periodic as DedicatedDaysPeriodic)
                                .days
                                .first
                                .closeAt
                                .format(context),
                          ],
                        ),
                      ),
                    if (periodic is DedicatedDaysPeriodic &&
                        !(periodic as DedicatedDaysPeriodic).areHaveTheSameTime)
                      WidgetSpan(
                        child: Padding(
                          padding: const EdgeInsetsDirectional.only(start: 0),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: List.generate(
                              (periodic as DedicatedDaysPeriodic).days.length,
                              (index) => Text(
                                "${DateTimeExt.dayName((periodic as DedicatedDaysPeriodic).days[index].weekDay)} ${LocaleKeys.from_to.tr(args: [(periodic as DedicatedDaysPeriodic).days[index].openAt.format(context), (periodic as DedicatedDaysPeriodic).days[index].closeAt.format(context)])}",
                              ),
                            ),
                          ),
                        ),
                      ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }
}
