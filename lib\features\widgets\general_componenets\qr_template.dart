import 'package:flutter/material.dart';
import 'package:gopal/core/style/assets/gen/assets.gen.dart';
import 'package:gopal/core/style/repo.dart';
import 'package:qr_flutter/qr_flutter.dart';

class QRTemplate extends StatelessWidget {
  final String content;
  final Color squaresColor;
  final double size;
  const QRTemplate({
    super.key,
    required this.content,
    this.squaresColor = StyleRepo.black,
    required this.size,
  });

  @override
  Widget build(BuildContext context) {
    return QrImageView(
      data: content,
      version: QrVersions.auto,
      size: size,
      padding: const EdgeInsets.all(14),
      eyeStyle: const QrEyeStyle(
        color: StyleRepo.blueViolet,
        eyeShape: QrEyeShape.square,
      ),
      dataModuleStyle: QrDataModuleStyle(
        color: squaresColor,
        dataModuleShape: QrDataModuleShape.square,
      ),
      embeddedImage: Assets.images.logo.provider(),
      embeddedImageStyle: QrEmbeddedImageStyle(size: Size.square(size / 7)),
    );
  }
}
