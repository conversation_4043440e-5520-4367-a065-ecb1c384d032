import 'dart:developer';

import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:gopal/core/config/app_builder.dart';
import 'package:gopal/core/services/rest_api/rest_api.dart';
import 'package:gopal/core/services/share/share.dart';
import 'package:gopal/core/widgets/loading.dart';

class InvitationPageController extends GetxController {
  AppBuilder appBuilder = Get.find();

  copyCode() {
    Clipboard.setData(ClipboardData(text: appBuilder.user.value!.code));
  }

  share() async {
    Loading.show();
    ResponseModel response = await APIService.instance.requestAPI(
      Request(
        endPoint: EndPoints.settings,
        params: {"types[0]": "APP_LINK_ANDROID", "types[1]": "APP_LINK_IOS"},
      ),
    );
    Loading.dispose();
    String? androidLink, iosLink;
    if (response.success) {
      try {
        androidLink = response.data[0]['value'];
        iosLink = response.data[1]['value'];
      } catch (_) {}
    }
    String message =
        '''صديقك ${appBuilder.user.value!.name} يدعوك لاكتشاف عالم جوبال، حيث تلتقي الأنشطة الرياضية، والترفيهية، والتعليمية في تجربة فريدة تجمع السهولة، والإبداع، والأمان.

✨ ابدأ رحلتك الآن باستخدام كود الدعوة: ${appBuilder.user.value!.code}

اختر شغفك، وابدأ مغامرتك – جوبال بانتظارك! 🚀

Your friend ${appBuilder.user.value!.name} invites you to discover the world of GoPal, where sports, entertainment, and educational activities come together in a unique experience that combines ease, creativity, and safety.

✨ Start your journey now using the invitation code: ${appBuilder.user.value!.code}

Choose your passion and start your adventure – GoPal is waiting for you! 🚀

📲 Android: ${androidLink ?? ""}
📲 iOS: ${iosLink ?? ""}
''';
    log(message);
    ShareHelper.share(message);
  }
}
