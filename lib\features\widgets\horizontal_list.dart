import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart';
import 'package:gopal/core/style/repo.dart';
import 'package:gopal/core/widgets/shimmer_loading.dart';
import 'package:gopal/features/widgets/general_componenets/fields/field_error.dart';

class HorizontalListLoading extends StatelessWidget {
  final String title;
  const HorizontalListLoading({super.key, required this.title});

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: Text(
            title,
            style: context.textTheme.titleLarge!.copyWith(
              color: StyleRepo.blueViolet,
            ),
          ),
        ),
        const Gap(12),
        SizedBox(
          height: 150,
          child: ListView.separated(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            scrollDirection: Axis.horizontal,
            itemCount: 8,
            separatorBuilder: (_, __) => const Gap(12),
            itemBuilder:
                (_, __) => ShimmerWidget.card(
                  width: Get.width * .7,
                  height: 150,
                  borderRadius: BorderRadius.circular(18),
                ),
          ),
        ),
      ],
    );
  }
}

class HorizontalListError extends StatelessWidget {
  final String title;
  final String error;
  final void Function()? onRefresh;
  const HorizontalListError({
    super.key,
    required this.title,
    required this.error,
    this.onRefresh,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: Text(
            title,
            textAlign: TextAlign.center,
            style: context.textTheme.titleLarge!.copyWith(
              color: StyleRepo.blueViolet,
            ),
          ),
        ),
        const Gap(12),
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 24),
          child: FieldErrorWidget(error: error, onRefresh: onRefresh),
        ),
      ],
    );
  }
}
