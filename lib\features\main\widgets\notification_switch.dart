import 'package:app_settings/app_settings.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:gopal/core/localization/strings.dart';
import 'package:gopal/core/services/local_notifications/local_notification.dart';
import 'package:gopal/features/widgets/general_componenets/svg_icon.dart';

import 'drawer_card.dart';

class NotificationSwitch extends StatefulWidget {
  const NotificationSwitch({super.key});

  @override
  State<NotificationSwitch> createState() => _NotificationSwitchState();
}

class _NotificationSwitchState extends State<NotificationSwitch>
    with WidgetsBindingObserver {
  @override
  void initState() {
    WidgetsBinding.instance.addObserver(this);
    super.initState();
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return DrawerCard(
      icon: SvgIcon(Assets.icons.notificationsOutlined.path),
      title: LocaleKeys.notifications.tr(),
      onTap:
          () => AppSettings.openAppSettings(type: AppSettingsType.notification),
      trailing: FutureBuilder(
        future: LocalNotificationService.areNotificationsEnabled(),
        builder: (context, snap) {
          if (snap.hasData) {
            return Switch(
              value: snap.data!,
              onChanged:
                  (_) => AppSettings.openAppSettings(
                    type: AppSettingsType.notification,
                  ),
            );
          }
          return const SizedBox();
        },
      ),
    );
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    if (state == AppLifecycleState.resumed) {
      setState(() {});
    }
  }
}
