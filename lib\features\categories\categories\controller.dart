import 'package:get/get.dart';
import 'package:gopal/core/models/category.dart';
import 'package:gopal/core/services/state_management/observable_variable.dart';

import '../../../core/services/rest_api/rest_api.dart';

class CategoriesPageController extends GetxController {
  ObsList<Category> categories = ObsList([]);
  fetchCategories() async {
    ResponseModel response = await APIService.instance.requestAPI(
      Request(endPoint: EndPoints.categories, fromJson: Category.fromJson),
    );
    if (response.success) {
      categories.value = response.data;
    } else {
      categories.error = response.message;
    }
  }

  refreshCategories() async {
    categories.reset();
    await fetchCategories();
  }

  @override
  void onInit() {
    fetchCategories();
    super.onInit();
  }
}
