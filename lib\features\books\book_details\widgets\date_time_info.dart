import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart' hide Trans;
import 'package:gopal/core/localization/strings.dart';
import 'package:gopal/core/models/activity/constants/type.dart';
import 'package:gopal/core/models/book/book.dart';
import 'package:gopal/core/style/repo.dart';
import 'package:gopal/features/activities/widgets/periodic_time_info.dart';
import 'package:gopal/features/widgets/general_componenets/svg_icon.dart';

import 'specific_days_sessions.dart';

class BookDateTimeInfo extends StatelessWidget {
  final BookedActivity book;
  const BookDateTimeInfo({super.key, required this.book});

  @override
  Widget build(BuildContext context) {
    switch (book.type) {
      case ActivityType.numbered_session:
        return Container(
          margin: const EdgeInsets.symmetric(horizontal: 16),
          decoration: BoxDecoration(
            color: StyleRepo.white,
            boxShadow: StyleRepo.elevation,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Padding(
                padding: const EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 12,
                ),
                child: Text(
                  LocaleKeys.booked_set.tr(),
                  style: context.textTheme.titleLarge!.copyWith(
                    color: StyleRepo.berkeleyBlue,
                  ),
                ),
              ),
              ...List.generate(
                book.sessions.length,
                (j) => Column(
                  children: [
                    const Divider(height: 2),
                    Container(
                      color:
                          book.sessions[j].date.isBefore(
                                DateTime.now().subtract(1.days),
                              )
                              ? StyleRepo.grey.shade200
                              : null,
                      child: ListTile(
                        title: Text(
                          "${LocaleKeys.session.tr()} ${j + 1}",
                          style: context.textTheme.titleSmall!.copyWith(
                            color: StyleRepo.berkeleyBlue,
                          ),
                        ),
                        trailing: Text(
                          DateFormat.yMd().format(book.sessions[j].date),
                        ),
                        subtitle: Text(
                          LocaleKeys.from_to.tr(
                            args: [
                              book.sessions[j].from.format(context),
                              book.sessions[j].to.format(context),
                            ],
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        );
      case ActivityType.periodic_days:
        return Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: PeriodicTimeInfo(periodic: book.periodic!),
        );
      case ActivityType.open_sessions:
      case ActivityType.custom_session:
        if (book.sessions.isEmpty) {
          return SizedBox();
        } else {
          return Container(
            margin: const EdgeInsets.symmetric(horizontal: 16),
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            decoration: BoxDecoration(
              color:
                  book.sessions.first.date.isBefore(
                        DateTime.now().subtract(1.days),
                      )
                      ? StyleRepo.grey.shade300
                      : StyleRepo.white,
              borderRadius: BorderRadius.circular(10),
              boxShadow: StyleRepo.elevation,
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  LocaleKeys.booked_session.tr(),
                  style: context.textTheme.titleSmall!.copyWith(
                    color: StyleRepo.berkeleyBlue,
                  ),
                ),
                const Gap(12),
                Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    SvgIcon(
                      Assets.icons.calendar.path,
                      size: 20,
                      color: StyleRepo.grey.shade700,
                    ),
                    const Gap(6),
                    Text(
                      "${LocaleKeys.date.tr()}: ",
                      style: context.textTheme.bodyMedium!.copyWith(
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    const Gap(6),
                    Expanded(
                      child: Text(
                        DateFormat.yMMMMd(
                          EasyLocalization.of(
                            context,
                          )!.currentLocale!.languageCode,
                        ).format(book.sessions.first.date),
                        style: const TextStyle(fontWeight: FontWeight.w500),
                      ),
                    ),
                  ],
                ),
                const Gap(12),
                Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    SvgIcon(
                      Assets.icons.time.path,
                      size: 20,
                      color: StyleRepo.grey.shade700,
                    ),
                    const Gap(6),
                    Text(
                      "${LocaleKeys.time.tr()}: ",
                      style: context.textTheme.bodyMedium!.copyWith(
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    Expanded(
                      child: Text(
                        LocaleKeys.from_to.tr(
                          args: [
                            book.sessions.first.from.format(context),
                            book.sessions.first.to.format(context),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          );
        }
      case ActivityType.specific_days:
        return SpecificDaysBookSessions(book: book);
      default:
        return const SizedBox();
    }
  }
}
