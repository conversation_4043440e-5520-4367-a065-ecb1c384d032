import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:gopal/core/localization/strings.dart';
import 'package:gopal/core/style/repo.dart';

import '../models/transaction.dart';

class PointTransactionCard extends StatelessWidget {
  final PointsTransaction transaction;
  const PointTransactionCard({super.key, required this.transaction});

  @override
  Widget build(BuildContext context) {
    return ListTile(
      leading:
          transaction.isUsed
              ? CircleAvatar(
                backgroundColor: StyleRepo.red,
                foregroundColor: StyleRepo.white,
                child: Icon(Icons.remove),
              )
              : CircleAvatar(
                backgroundColor: StyleRepo.blueViolet,
                foregroundColor: StyleRepo.white,
                child: Icon(Icons.add),
              ),
      title: RichText(
        text: TextSpan(
          style: Theme.of(context).textTheme.bodyLarge,
          children: [
            TextSpan(
              text: "${LocaleKeys.n_points.plural(transaction.points)} ",
              style: TextStyle(
                fontWeight: FontWeight.bold,
                color: StyleRepo.berkeleyBlue,
              ),
            ),
            TextSpan(
              text:
                  "${(transaction.isUsed ? LocaleKeys.used_at : LocaleKeys.earned_at).tr()} ${DateFormat("yyyy/MM/dd", EasyLocalization.of(context)!.currentLocale!.languageCode).format(transaction.createdAt)}",
            ),
            if (!transaction.isUsed)
              TextSpan(
                text:
                    " ${LocaleKeys.expire_at.tr()} ${DateFormat("yyyy/MM/dd", EasyLocalization.of(context)!.currentLocale!.languageCode).format(transaction.expirationAt)}",
              ),
          ],
        ),
      ),
    );
    // return Container(
    //   decoration: BoxDecoration(
    //     border: Border.all(),
    //     borderRadius: BorderRadius.circular(15),
    //   ),
    //   child: Text(data),
    // );
  }
}
