import 'package:get/get.dart';
import 'package:gopal/core/models/activity/main_activity.dart';
import 'package:gopal/core/models/chat/chat_room.dart';
import 'package:gopal/core/routes.dart';
import 'package:gopal/core/services/state_management/observable_variable.dart';
import 'package:gopal/features/activities/activities/models/nav.dart';

import '../../../core/services/rest_api/rest_api.dart';
import '../models/center_review.dart';
import 'models/center_details.dart';
import 'models/nav.dart';

class CenterProfilePageController extends GetxController {
  final CenterProfilePageNav nav;
  CenterProfilePageController(this.nav);

  late Request<CenterReview> reviewsRequest;

  @override
  void onInit() {
    fetchCenter();
    fetchActivities();
    fetchReviews();
    super.onInit();
  }

  Future<void> refreshData() async {
    refreshCenter();
    refreshActivities();
    refreshReviews();
  }

  //SECTION - Center details
  ObsVar<CenterDetails> center = ObsVar(null);

  fetchCenter() async {
    ResponseModel response = await APIService.instance.requestAPI(
      Request(
        endPoint: EndPoints.center_details(nav.id),
        fromJson: CenterDetails.fromJson,
      ),
    );
    if (response.success) {
      center.value = response.data;
    } else {
      center.error = response.message;
    }
  }

  refreshCenter() async {
    center.reset();
    await fetchCenter();
  }
  //!SECTION

  //SECTION - Activities
  ObsList<MainActivity> activities = ObsList([]);

  fetchActivities() async {
    ResponseModel response = await APIService.instance.requestAPI(
      Request(
        endPoint: EndPoints.main_activities,
        params: {"provider_id": nav.id},
        fromJson: MainActivity.fromJson,
      ),
    );
    if (response.success) {
      activities.value = response.data;
    } else {
      activities.error = response.message;
    }
  }

  refreshActivities() async {
    activities.reset();
    await fetchActivities();
  }
  //!SECTION

  //SECTION - Reviews
  ObsList<CenterReview> reviews = ObsList([]);

  fetchReviews() async {
    ResponseModel response = await APIService.instance.requestAPI(
      Request(
        endPoint: EndPoints.center_reviews,
        params: {
          "page": 1,
          "page-size": 3,
          "reviewable_type": "PROVIDER",
          "reviewable_id": nav.id,
        },
        fromJson: CenterReview.fromJson,
      ),
    );
    if (response.success) {
      reviews.value = response.data;
    } else {
      reviews.error = response.message;
    }
  }

  refreshReviews() async {
    reviews.reset();
    await fetchReviews();
  }
  //!SECTION

  seeAllActivities() {
    Nav.to(
      Pages.activities,
      arguments: ActivitiesPageNav(
        title: center.value!.name,
        endPoint: EndPoints.main_activities,
        params: {"provider_id": nav.id},
      ),
    );
  }

  navToChat(ChatRoom room) async {
    Nav.to(Pages.chat_room, arguments: room);
  }
}
