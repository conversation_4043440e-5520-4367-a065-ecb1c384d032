import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart' hide Trans;
import 'package:gopal/core/constants/controllers_tags.dart';
import 'package:gopal/core/localization/strings.dart';
import 'package:gopal/core/models/book/book.dart';
import 'package:gopal/core/services/pagination/options/list_view.dart';
import 'package:gopal/core/style/repo.dart';
import 'package:gopal/features/usecases/confirmation/index.dart';
import 'package:gopal/features/widgets/general_componenets/svg_icon.dart';

import '../../../books/widgets/book_card.dart';
import '../controller.dart';
import 'loading.dart';

class MyPendingActivities extends GetView<MyActivitiesPageController> {
  const MyPendingActivities({super.key});

  @override
  Widget build(BuildContext context) {
    return ListViewPagination<BookedActivity>.separated(
      tag: ControllersTags.my_pending_activities,
      fromJson: BookedActivity.fromJson,
      fetchApi: controller.fetchPending,
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      separatorBuilder: (_, __) => const Gap(12),
      initialLoading: const BooksLoading(),
      itemBuilder: (context, index, book) {
        return BookCard(
          bookedActivity: book,
          trailing: PopupMenuButton(
            surfaceTintColor: StyleRepo.white,
            itemBuilder:
                (context) => <PopupMenuEntry>[
                  PopupMenuItem(
                    onTap:
                        () => ConfirmationDialog.confirm(
                          onAccept: () => controller.deleteBook(book),
                          body: LocaleKeys.delete_book_confirmation.tr(),
                          confirmText: LocaleKeys.delete.tr(),
                        ),
                    child: Row(
                      children: [
                        SvgIcon(Assets.icons.delete.path, color: StyleRepo.red),
                        const Gap(8),
                        Text(
                          LocaleKeys.delete.tr(),
                          style: const TextStyle(color: StyleRepo.red),
                        ),
                      ],
                    ),
                  ),
                ],
            child: SvgIcon(Assets.icons.menu.path),
          ),
        );
      },
    );
  }
}
