import 'dart:math';

import 'package:easy_localization/easy_localization.dart';
import 'package:get/get.dart';
import 'package:gopal/core/config/app_builder.dart';
import 'package:gopal/core/config/defaults.dart';
import 'package:gopal/core/config/role_middleware.dart';
import 'package:gopal/core/localization/strings.dart';
import 'package:gopal/core/models/activity/constants/type.dart';
import 'package:gopal/core/models/activity/session.dart';
import 'package:gopal/core/models/user/main_child.dart';
import 'package:gopal/core/services/rest_api/rest_api.dart';
import 'package:gopal/core/utils/date.dart';
import 'package:gopal/core/widgets/loading.dart';
import 'package:gopal/features/activities/usecases/persons_count_section/persons_selection_count.dart';
import 'package:gopal/features/usecases/book_custom_session/index.dart';
import 'package:gopal/features/usecases/children_selection/widgets/children_selection_dialog.dart';
import 'package:gopal/features/usecases/toast/status.dart';
import 'package:gopal/features/usecases/toast/toast.dart';
import 'package:url_launcher/url_launcher.dart';

import '../points_selection/points_selection.dart';

typedef PointsSettings = ({int points, int maxPoints, double pointValue});

mixin Booking {
  //SECTION - Booking
  bookASet({required int activityId, required int setId}) async {
    return await _mainBook(
      activityId: activityId,
      type: ActivityType.numbered_session,
      bookableId: setId,
    );
  }

  bookAPeriodic({required int activityId, required int periodId}) async {
    return await _mainBook(
      activityId: activityId,
      type: ActivityType.periodic_days,
      bookableId: periodId,
    );
  }

  bookATicket({
    required int activityId,
    required int minPerson,
    required int maxPerson,
  }) async {
    return await _mainBook(
      activityId: activityId,
      type: ActivityType.ticket,
      supportChildSelection: false,
      bookableId: activityId,
      minPersons: minPerson,
      maxPersons: maxPerson,
      otherBody: {"bookable_type": "SUB_ACTIVITY"},
    );
  }

  bookASpecificSession({
    required int activityId,
    required int specificDayId,
  }) async {
    return await _mainBook(
      activityId: activityId,
      type: ActivityType.specific_days,
      bookableId: specificDayId,
    );
  }

  bookAnOpenSession({
    required int activityId,
    required ActivitySession session,
    int? minPersons,
    int? maxPersons,
  }) async {
    return await _mainBook(
      activityId: activityId,
      type: ActivityType.open_sessions,
      bookableId: session.openSessionId!,
      canBookMulti: true,
      otherBody: {
        "session_values_ids": [session.id],
      },
      minPersons: minPersons,
      maxPersons: maxPersons,
    );
  }

  bookCustomSession({
    required int activityId,
    int? minPersons,
    int? maxPersons,
  }) async {
    if (RoleMiddleware.guestForbidden) return;

    List<DateTimeSelectionResult>? sessionsTimes =
        await CustomSessionDialog.open();
    if (sessionsTimes == null) return;

    return await _mainBook(
      activityId: activityId,
      type: ActivityType.custom_session,
      canBookMulti: true,
      minPersons: minPersons,
      maxPersons: maxPersons,
      otherBody: {
        "sub_activity_id": activityId,
        "custom_sessions":
            sessionsTimes
                .map(
                  (e) => {
                    "date": e.date.toString(),
                    "from": e.from.value(),
                    "to": e.to.value(),
                  },
                )
                .toList(),
      },
    );
  }

  bookCustomSessionForAnotherType({
    required int activityId,
    int? minPersons,
    int? maxPersons,
  }) async {
    if (RoleMiddleware.guestForbidden) return;

    List<DateTimeSelectionResult>? sessionsTimes =
        await CustomSessionDialog.open();
    if (sessionsTimes == null) return;

    return _mainBook(
      activityId: activityId,
      type: ActivityType.custom_session,
      bookableId: activityId,
      canBookMulti: true,
      minPersons: minPersons,
      maxPersons: maxPersons,
      otherBody: {
        "bookable_type": "SUB_ACTIVITY",
        "custom_sessions":
            sessionsTimes
                .map(
                  (e) => {
                    "date": e.date.toString(),
                    "from": e.from.value(),
                    "to": e.to.value(),
                  },
                )
                .toList(),
      },
    );
  }

  _mainBook({
    required int activityId,
    required ActivityType type,
    int? bookableId,
    Map<String, dynamic>? otherBody,
    bool canBookMulti = false,
    bool supportChildSelection = true,
    int? minPersons,
    int? maxPersons,
  }) async {
    if (RoleMiddleware.guestForbidden) return;
    List<MainChild>? selectedChildren;

    if (supportChildSelection) {
      selectedChildren = await ChildrenSelectionDialog.open(
        activityId: canBookMulti ? null : activityId,
        isMultiSelect: false,
        seperateParent: true,
        //TODO - book for more than one child
        //NOTE - book for more than one child

        // isMultiSelect: canBookMulti,
      );
      if (selectedChildren == null) return;
    }

    int? personsCount;

    if (minPersons != null && maxPersons != null) {
      personsCount = await Get.dialog(
        PersonsSelectionDialog(minPersons: minPersons, maxPersons: maxPersons),
      );

      if (personsCount == null) return;
    }

    int? points = await _getPoints();

    if (points == null) {
      return;
    }

    Loading.show();
    Map<String, dynamic> body = {
      "points": points,
      if (personsCount != null) "persons_count": personsCount,
      if (selectedChildren != null)
        "children_ids": selectedChildren.map((e) => e.id).toList(),
      "bookable_type": type.value,
      if (bookableId != null) "bookable_id": bookableId,
    };
    if (otherBody != null) {
      body.addAll(otherBody);
    }
    ResponseModel response = await APIService.instance.requestAPI(
      Request(
        endPoint: EndPoints.book_activity,
        method: RequestMethod.Post,
        body: body,
      ),
    );
    Loading.dispose();
    if (response.success) {
      if (Get.find<AppBuilder>().user.value!.phone == Default.testNumber) {
        return;
      }
      if (response.data['redirect_url'] == null) {
        Toast.show(
          message: tr(LocaleKeys.book_success_msg),
          status: ToastStatus.success,
        );
        return;
      }
      launchUrl(
        Uri.parse(response.data['redirect_url']),
        mode: LaunchMode.externalApplication,
      );
    } else {
      if (response.errorType is VALIDATION_WARNING_ERROR) {
        Toast.show(message: response.message, status: ToastStatus.warning);
        return;
      }
      Toast.show(message: response.message);
    }
  }

  //!SECTION

  //SECTION - Points processing
  Future<int?> _getPoints() async {
    PointsSettings? points = await _getUserPoints();

    if (points == null || points.points < 10) {
      return 0;
    }

    int? selectedPoints = await Get.dialog(
      PointsSelectionDialog(
        maxPoints: min(points.maxPoints, points.points),
        myPoints: points.points,
        pointValue: points.pointValue,
      ),
    );

    return selectedPoints;
  }

  Future<PointsSettings?> _getUserPoints() async {
    Loading.show();
    ResponseModel response = await APIService.instance.requestAPI(
      Request(endPoint: EndPoints.points_data),
    );
    Loading.dispose();
    if (response.success) {
      return (
        points: int.parse("${response.data['porints_count']}"),
        maxPoints: int.parse("${response.data['max_point_to_repalce']}"),
        pointValue: double.parse("${response.data['point_value_sar']}"),
      );
    } else {
      Toast.show(message: response.message);
      return null;
    }
  }

  //!SECTION
}
