import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart' hide Trans;
import 'package:gopal/core/localization/strings.dart';
import 'package:gopal/core/models/activity/main_activity.dart';
import 'package:gopal/core/routes.dart';
import 'package:gopal/core/style/repo.dart';
import 'package:gopal/core/utils/num_utils.dart';
import 'package:gopal/core/widgets/image.dart';
import 'package:gopal/features/activities/main_activity/models/nav.dart';
import 'package:gopal/features/widgets/general_componenets/banner.dart';
import 'package:gopal/features/widgets/general_componenets/svg_icon.dart';
import 'package:gopal/features/widgets/provider_image.dart';

class ActivityCard extends StatelessWidget {
  final MainActivity activity;
  final bool isOffer;

  const ActivityCard({super.key, required this.activity, this.isOffer = false});

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap:
          () =>
              activity.subActivityId != null
                  ? Nav.to(
                    Pages.activity_details,
                    arguments: activity.subActivityId,
                    preventDuplicates: false,
                  )
                  : Nav.to(
                    Pages.main_activity,
                    arguments: MainActivityPageNav(id: activity.id),
                    preventDuplicates: false,
                  ),
      child: Container(
        width: Get.width * .7,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(18),
          boxShadow: StyleRepo.elevation,
        ),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(18),
          child: BanneredWidget(
            message: "discount",
            location: BannerLocation.topEnd,
            color: StyleRepo.turquoise,
            isVisible: isOffer,
            child: Opacity(
              opacity: activity.isAvailable ? 1 : .6,
              child: Container(
                padding: const EdgeInsets.all(4),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(18),
                  color: StyleRepo.white,
                  gradient:
                      isOffer
                          ? LinearGradient(
                            colors: [
                              StyleRepo.turquoise,
                              StyleRepo.turquoise.withValues(alpha: .2),
                              StyleRepo.turquoise,
                            ],
                            begin: AlignmentDirectional.topStart,
                            end: AlignmentDirectional.bottomEnd,
                          )
                          : null,
                ),
                child: Container(
                  decoration: BoxDecoration(
                    color: StyleRepo.white,
                    borderRadius: BorderRadius.circular(18),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Stack(
                        children: [
                          Padding(
                            padding: const EdgeInsets.only(bottom: 25),
                            child: AspectRatio(
                              aspectRatio: 16 / 9,
                              child: AppImage(
                                path: activity.cover.large,
                                type: ImageType.CachedNetwork,
                                decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(18),
                                ),
                                height: double.infinity,
                                width: double.infinity,
                              ),
                            ),
                          ),
                          PositionedDirectional(
                            bottom: 0,
                            start: 12,
                            end: 12,
                            child: Row(
                              crossAxisAlignment: CrossAxisAlignment.end,
                              children: [
                                CenterImage(
                                  image: activity.center.profileImage.small,
                                  isTrusted: activity.center.isTrusted,
                                  radius: 50,
                                ),
                                const Gap(10),
                                Expanded(
                                  child: Text(
                                    activity.center.name,
                                    style: const TextStyle(
                                      fontWeight: FontWeight.bold,
                                    ),
                                    overflow: TextOverflow.ellipsis,
                                  ),
                                ),
                                const Gap(8),
                                if (activity.booksCount >= 1)
                                  Text(
                                    LocaleKeys.n_books.plural(
                                      activity.booksCount,
                                      args: [activity.booksCount.approximate],
                                    ),
                                    style: context.textTheme.labelMedium!
                                        .copyWith(
                                          color: StyleRepo.berkeleyBlue,
                                          fontWeight: FontWeight.bold,
                                        ),
                                  ),
                              ],
                            ),
                          ),
                        ],
                      ),
                      const Gap(10),
                      Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 10),
                        child: Row(
                          children: [
                            Expanded(
                              child: Text(
                                activity.name,
                                style: context.textTheme.bodyMedium,
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                            const Gap(10),
                            Row(
                              children: [
                                SvgIcon(
                                  Assets.icons.star.path,
                                  color: StyleRepo.yellow.shade600,
                                  size: 17,
                                ),
                                const Gap(6),
                                Text(
                                  activity.center.rate.toStringAsFixed(1),
                                  style: context.textTheme.labelMedium,
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),
                      const Gap(10),
                      Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 10),
                        child: Text(
                          activity.description,
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                          style: context.textTheme.bodySmall!.copyWith(
                            color: StyleRepo.grey,
                          ),
                        ),
                      ),
                      const Gap(10),
                    ],
                  ),
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }
}
