import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart';
import 'package:gopal/core/constants/controllers_tags.dart';
import 'package:gopal/core/localization/strings.dart';
import 'package:gopal/core/models/book/book.dart';
import 'package:gopal/core/services/pagination/options/list_view.dart';
import 'package:gopal/core/style/repo.dart';
import 'package:gopal/features/activities/my_activities/widgets/loading.dart';
import 'package:gopal/features/books/widgets/book_card.dart';
import 'package:gopal/features/usecases/confirmation/index.dart';
import 'package:gopal/features/widgets/general_componenets/app_bars/general_app_bar.dart';
import 'package:gopal/features/widgets/general_componenets/svg_icon.dart';

import 'controller.dart';

class CartPage extends StatelessWidget {
  const CartPage({super.key});

  @override
  Widget build(BuildContext context) {
    // ignore: unused_local_variable
    CartPageController controller = Get.put(CartPageController());
    return Scaffold(
      appBar: GeneralAppBar(title: Text(tr(LocaleKeys.cart))),
      body: ListViewPagination<BookedActivity>.separated(
        tag: ControllersTags.my_pending_activities,
        fromJson: BookedActivity.fromJson,
        fetchApi: controller.fetchPending,
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        separatorBuilder: (_, __) => const Gap(12),
        initialLoading: const BooksLoading(),
        itemBuilder: (context, index, book) {
          return BookCard(
            bookedActivity: book,
            trailing: PopupMenuButton(
              surfaceTintColor: StyleRepo.white,
              itemBuilder:
                  (context) => <PopupMenuEntry>[
                    PopupMenuItem(
                      onTap:
                          () => ConfirmationDialog.confirm(
                            onAccept: () => controller.deleteBook(book),
                            body: tr(LocaleKeys.delete_book_confirmation),
                            confirmText: tr(LocaleKeys.delete),
                          ),
                      child: Row(
                        children: [
                          SvgIcon(
                            Assets.icons.delete.path,
                            color: StyleRepo.red,
                          ),
                          const Gap(8),
                          Text(
                            tr(LocaleKeys.delete),
                            style: const TextStyle(color: StyleRepo.red),
                          ),
                        ],
                      ),
                    ),
                  ],
              child: SvgIcon(Assets.icons.menu.path),
            ),
          );
        },
      ),
    );
  }
}
