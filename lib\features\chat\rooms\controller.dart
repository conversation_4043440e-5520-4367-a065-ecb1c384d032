import 'package:dio/dio.dart';
import 'package:get/get.dart';
import 'package:gopal/core/constants/controllers_tags.dart';
import 'package:gopal/core/models/chat/chat_room.dart';
import 'package:gopal/core/services/rest_api/rest_api.dart';
import 'package:gopal/core/services/state_management/observable_variable.dart';

import '../../../core/services/pagination/controller.dart';

class ChatRoomsPageController extends GetxController {
  Future<ResponseModel> fetchRooms(int page, CancelToken cancel) async {
    ResponseModel response = await APIService.instance.requestAPI(
      Request(
        endPoint: EndPoints.rooms,
        params: {"page": page},
        cancelToken: cancel,
      ),
    );

    //TODO - dirty code
    //SECTION - Dirty code to remove dirty data
    if (response.success) {
      (response.data as List).removeWhere(
        (element) => element['providers'].isEmpty,
      );
    }
    return response;
    //!SECTION
  }

  PaginationController<ChatRoom> get paginationController =>
      Get.find<PaginationController<ChatRoom>>(
        tag: ControllersTags.chat_rooms_pager,
      );
  ObsList<ChatRoom> get rooms => paginationController.data;

  reorderRooms({required int roomId, required String preview}) {
    if (rooms.value?.firstWhereOrNull((element) => element.id == roomId) !=
        null) {
      ChatRoom room = rooms.removeWhere((element) => element.id == roomId)!;
      room.messagePreview = preview;
      room.date = DateTime.now();
      rooms.insert(0, room);
    } else {
      paginationController.refreshData();
    }
  }
}
