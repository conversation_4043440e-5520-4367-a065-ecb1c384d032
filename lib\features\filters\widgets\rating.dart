import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_rating_bar/flutter_rating_bar.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart' hide Trans;
import 'package:gopal/core/localization/strings.dart';
import 'package:gopal/core/style/repo.dart';
import 'package:gopal/features/widgets/general_componenets/svg_icon.dart';

import '../controller.dart';

class RatingFilter extends GetView<FiltersPageController> {
  const RatingFilter({super.key});

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Gap(24),
        Text(
          LocaleKeys.rating.tr(),
          style: context.textTheme.titleMedium!.copyWith(
            fontWeight: FontWeight.w700,
          ),
        ),
        const Gap(6),
        ...List.generate(
          4,
          (index) {
            return InkWell(
              onTap: () => controller.rating = index != 0 ? index + 1 : index,
              child: Obx(
                () => Align(
                  heightFactor: .8,
                  child: Row(
                    children: [
                      Radio(
                        value: index != 0 ? index + 1 : index,
                        groupValue: controller.rating,
                        onChanged: (value) =>
                            value != null ? controller.rating = value : null,
                      ),
                      if (index == 0)
                        Text(
                          LocaleKeys.any.tr(),
                          style: TextStyle(
                            fontWeight:
                                controller.rating == 0 ? FontWeight.bold : null,
                          ),
                        ),
                      if (index != 0)
                        RatingBar.builder(
                          ignoreGestures: true,
                          initialRating: index + 1,
                          maxRating: index + 1,
                          itemCount: index + 1,
                          onRatingUpdate: (_) {},
                          itemSize: 23,
                          unratedColor: Colors.transparent,
                          itemBuilder: (_, __) => SvgIcon(
                            Assets.icons.star.path,
                            color: StyleRepo.yellow.shade600,
                          ),
                        ),
                      if (index != 0) const Gap(6),
                      if (index != 0)
                        Text(
                          LocaleKeys.and_up.tr(),
                          style: TextStyle(
                            fontWeight: controller.rating == (index + 1)
                                ? FontWeight.bold
                                : null,
                          ),
                        )
                    ],
                  ),
                ),
              ),
            );
          },
        ),
      ],
    );
    // return Column(
    //   crossAxisAlignment: CrossAxisAlignment.start,
    //   children: [
    //     const Gap(24),
    //     Text(
    //       LocaleKeys.rating.tr(),
    //       style: context.textTheme.titleMedium!.copyWith(
    //         fontWeight: FontWeight.w700,
    //       ),
    //     ),
    //     const Gap(6),
    //     Obx(
    //       () => RatingBar.builder(
    //         itemCount: 5,
    //         minRating: 2,
    //         maxRating: 5,
    //         initialRating: controller.rating,
    //         onRatingUpdate: (value) => controller.rating = value,
    //         wrapAlignment: WrapAlignment.center,
    //         itemSize: 35,
    //         glow: false,
    //         itemPadding:
    //             EdgeInsets.symmetric(horizontal: (Get.width - 35 * 5) * .075),
    //         unratedColor: StyleRepo.grey.shade300,
    //         allowHalfRating: true,
    //         itemBuilder: (_, __) => SvgIcon(
    //           Assets.icons.star.path,
    //           color: StyleRepo.yellow.shade600,
    //         ),
    //       ),
    //     ),
    //   ],
    // );
  }
}
