import 'package:dio/dio.dart';
import 'package:get/get.dart';

import '../../../core/services/rest_api/rest_api.dart';

class SupportRepliesPageController extends GetxController {
  Future<ResponseModel> fetchData(int page, CancelToken cancel) async {
    return await APIService.instance.requestAPI(
      Request(
        endPoint: EndPoints.targeted_question,
        params: {"page": page},
        cancelToken: cancel,
      ),
    );
  }
}
