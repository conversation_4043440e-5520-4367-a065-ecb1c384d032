import 'package:get/get.dart';
import 'package:gopal/core/services/state_management/observable_variable.dart';

import '../../../core/services/rest_api/rest_api.dart';

class AboutPageController extends GetxController {
  ObsVar<String> about = ObsVar(null);
  fetchAbout() async {
    ResponseModel response = await APIService.instance.requestAPI(
      Request(
        endPoint: EndPoints.settings,
        params: {"type": "ABOUT_US"},
        fromJson: (json) => json['text'],
      ),
    );
    if (response.success) {
      about.value = response.data.first;
    } else {
      about.error = response.message;
    }
  }

  @override
  void onInit() {
    fetchAbout();
    super.onInit();
  }
}
