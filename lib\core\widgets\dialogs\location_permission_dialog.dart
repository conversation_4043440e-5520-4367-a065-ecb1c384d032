import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart';
import 'package:gopal/core/style/assets/gen/assets.gen.dart';
import 'package:gopal/features/widgets/general_componenets/buttons/elevated_button.dart';

import '../../localization/strings.dart';

class LocationPermissionDialog extends StatelessWidget {
  const LocationPermissionDialog({super.key});

  static Future<bool> open() async =>
      (await Get.dialog(const LocationPermissionDialog())) ?? false;

  @override
  Widget build(BuildContext context) {
    return Dialog(
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: <Widget>[
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const SizedBox(width: 50),
              Expanded(
                child: Padding(
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  child: Assets.icons.location.svg(height: 60),
                ),
              ),
              IconButton(
                onPressed: () => Get.back(result: false),
                icon: const Icon(Icons.close),
              ),
            ],
          ),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  tr(LocaleKeys.access_your_current_location),
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.w700,
                  ),
                ),
                const Gap(8),
                Text(
                  tr(
                    LocaleKeys
                        .to_get_perfect_experience_you_need_to_activate_the_access_to_your_location,
                  ),
                  style: const TextStyle(),
                ),
              ],
            ),
          ),
          const Gap(24),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: AppElevatedButton(
              onPressed: () => Get.back(result: true),
              height: 40,
              child: Text(tr(LocaleKeys.confirm)),
            ),
          ),
          const Gap(12),
        ],
      ),
    );
  }
}
