import 'dart:io';

import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart';
import 'package:gopal/core/style/assets/gen/assets.gen.dart';
import 'package:gopal/core/style/repo.dart';
import 'package:gopal/features/widgets/general_componenets/buttons/elevated_button.dart';
import 'package:gopal/features/widgets/general_componenets/buttons/outlined_button.dart';
import 'package:url_launcher/url_launcher_string.dart';

import '../../localization/strings.dart';
import '../../models/errors/version_update_data.dart';

class UpdateAppDialog extends StatelessWidget {
  final bool canSkip;
  final VersionUpdateData data;
  const UpdateAppDialog({super.key, required this.canSkip, required this.data});

  static Future<bool> open({
    required bool canSkip,
    required VersionUpdateData data,
  }) async =>
      (await Get.dialog(
        UpdateAppDialog(canSkip: canSkip, data: data),
        barrierDismissible: canSkip,
      )) ??
      false;

  @override
  Widget build(BuildContext context) {
    return PopScope(
      onPopInvokedWithResult: (_, __) => exit(0),
      child: Center(
        child: Padding(
          padding: EdgeInsets.symmetric(horizontal: Get.width * .1),
          child: Material(
            color: Colors.transparent,
            child: Stack(
              alignment: Alignment.topCenter,
              children: [
                Container(
                  padding: const EdgeInsets.fromLTRB(16, 40, 16, 16),
                  margin: const EdgeInsets.only(top: 50),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(35),
                    color: StyleRepo.white,
                  ),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Text(
                        tr(LocaleKeys.update_app),
                        style: context.textTheme.titleMedium!.copyWith(
                          color: StyleRepo.grey.shade700,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      const Gap(12),
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            tr(
                              LocaleKeys.this_version_will_expire_at_x,
                              args: [
                                DateFormat.yMd("en").format(data.expireDate),
                              ],
                            ),
                          ),
                          const Gap(12),
                          Text(tr(LocaleKeys.download_the_new_version_now)),
                        ],
                      ),
                      const Gap(12),
                      Row(
                        children: [
                          if (canSkip)
                            Expanded(
                              child: AppOutlinedButton(
                                onTap: () => Get.back(),
                                child: Text(tr(LocaleKeys.skip)),
                              ),
                            ),
                          if (canSkip) const Gap(12),
                          Expanded(
                            child: AppElevatedButton(
                              onPressed: () async {
                                await launchUrlString(data.appLink);
                                // exit(0);
                              },
                              child: Text(tr(LocaleKeys.update)),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
                Assets.icons.profileSticker.svg(),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
