import 'dart:developer';

import 'package:dio/dio.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/foundation.dart';
import 'package:gopal/core/models/errors/version_update_data.dart';
import 'package:gopal/core/services/version_checker/checker.dart';

import '../constants/api_error.dart';
import '../constants/messages.dart';
import '../models/exceptions.dart';
import '../models/response_model.dart';
import '../utilitis/parser.dart';

///Used for General Response Modelling in success case
ResponseModel responseModelling<T>(
  Response response, {
  T Function(Map<String, dynamic> json)? fromJson,
  bool withLog = false,
}) {
  if (withLog) {
    log(response.toString(), name: "API SERVICE - SUCCESS HANDLER");
  }

  switch (response.statusCode) {
    case 200:
    case 201:
      if (response.data is! Map<String, dynamic>) {
        throw ModellingException(
          'Response.data is not subtype of Type Map<String,dynamic>',
        );
      }
      if (response.data['update_settings'] != null) {
        VersionUpdateData? versionUpdateData = VersionUpdateData.fromJson(
          response.data['update_settings'],
        );

        VersionChecker().getInvalidVersionInAPI(versionUpdateData, true);
      }
      var data = response.data['data'];
      if (fromJson != null) {
        try {
          data = Parser.parsingData(data, fromJson);
        } catch (e) {
          data = null;
          if (kDebugMode) {
            rethrow;
          }
        }
        if (data == null) {
          throw ModellingException("Error when recieving data");
        }
      }
      return ResponseModel(
        success: true,
        message: "",
        data: data,
        statusCode: response.statusCode,
      );
    case 204:
      return ResponseModel(
        success: true,
        errorType: NO_CONTENT(),
        data: [],
        message: APIErrorMessages.noData.tr(),
      );
    default:
      return ResponseModel(success: true, message: "", data: response.data);
  }
}
