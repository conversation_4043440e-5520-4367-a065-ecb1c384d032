import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart' hide Trans;
import 'package:gopal/core/localization/strings.dart';
import 'package:gopal/features/widgets/general_componenets/buttons/elevated_button.dart';
import 'package:gopal/features/widgets/general_componenets/pages/rounded_page.dart';

import 'controller.dart';
import 'widgets/form.dart';

class EditProfilePage extends GetView<EditProfilePageController> {
  const EditProfilePage({super.key});

  @override
  Widget build(BuildContext context) {
    return RoundedPage(
      title: Text(LocaleKeys.edit_profile.tr()),
      child: ListView(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        children: [
          const EditProfileForm(),
          const Gap(24),
          AppElevatedButton(
            onPressed: () async => await controller.confirm(),
            child: Text(LocaleKeys.confirm.tr()),
          ),
        ],
      ),
    );
  }
}
