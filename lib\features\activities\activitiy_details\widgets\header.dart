import 'package:flutter/material.dart';
import 'package:flutter_rating_bar/flutter_rating_bar.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart' hide Trans;
import 'package:gopal/core/routes.dart';
import 'package:gopal/core/style/repo.dart';
import 'package:gopal/core/utils/num_utils.dart';
import 'package:gopal/features/center/profile/models/nav.dart';
import 'package:gopal/features/widgets/general_componenets/svg_icon.dart';
import 'package:gopal/features/widgets/provider_image.dart';

import '../controller.dart';
import '../models/activity.dart';

class ActivityDetailsHeader extends StatelessWidget {
  final ActivityDetails activity;
  const ActivityDetailsHeader({super.key, required this.activity});

  @override
  Widget build(BuildContext context) {
    ActivityDetailsPageController controller = Get.find(tag: "${activity.id}");
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: InkWell(
        onTap:
            () => Nav.to(
              Pages.center,
              arguments: CenterProfilePageNav(activity.center.id),
              preventDuplicates: false,
            ),
        child: Row(
          children: [
            CenterImage(
              image: activity.center.profileImage.small,
              isTrusted: activity.center.isTrusted,
              radius: 70,
            ),
            const Gap(10),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    activity.center.name,
                    textAlign: TextAlign.start,
                    style: context.textTheme.bodyLarge!.copyWith(
                      fontWeight: FontWeight.w600,
                      color: StyleRepo.blueViolet,
                    ),
                  ),
                  RatingBar.builder(
                    itemSize: 20,
                    unratedColor: Colors.transparent,
                    allowHalfRating: true,
                    ignoreGestures: true,
                    initialRating: activity.center.rate,
                    itemBuilder: (_, __) {
                      return SvgIcon(
                        Assets.icons.star.path,
                        color: StyleRepo.yellow.shade600,
                      );
                    },
                    onRatingUpdate: (_) {},
                  ),
                  if (activity.distance != null) const Gap(4),
                  if (activity.distance != null)
                    Text(
                      activity.distance!.approximateDistance,
                      style: TextStyle(
                        color: StyleRepo.grey.shade600,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                ],
              ),
            ),
            const Gap(10),
            IconButton(
              onPressed: () => controller.changeLikeStatus(),
              style: ButtonStyle(
                side: WidgetStatePropertyAll(
                  BorderSide(color: StyleRepo.grey.shade600),
                ),
              ),
              icon: Obx(
                () =>
                    controller.isLiked
                        ? Assets.icons.filledHeart.svg()
                        : Assets.icons.outlinedHeart.svg(),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
