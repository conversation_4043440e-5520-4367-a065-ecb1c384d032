import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:gopal/core/constants/controllers_tags.dart';
import 'package:gopal/core/localization/strings.dart';
import 'package:gopal/core/services/pagination/options/list_view.dart';
import 'package:gopal/features/widgets/general_componenets/pages/rounded_page.dart';

import 'controller.dart';
import 'models/transaction.dart';
import 'widgets/point_transaction_card.dart';

class PointsTransactionsPage extends StatelessWidget {
  const PointsTransactionsPage({super.key});

  @override
  Widget build(BuildContext context) {
    // ignore: unused_local_variable
    PointsTransactionsPageController controller = Get.put(
      PointsTransactionsPageController(),
    );
    return RoundedPage(
      title: Text(tr(LocaleKeys.points_history)),
      child: ListViewPagination.separated(
        tag: ControllersTags.points_history_pager,
        fetchApi: controller.fetchPointsTransactions,
        fromJson: PointsTransaction.from<PERSON>son,
        padding: EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        separatorBuilder: (_, __) => Divider(),
        itemBuilder:
            (context, index, transaction) =>
                PointTransactionCard(transaction: transaction),
      ),
    );
  }
}
