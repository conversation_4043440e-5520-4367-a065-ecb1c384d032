import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart' hide Trans;
import 'package:gopal/core/localization/strings.dart';
import 'package:gopal/core/style/repo.dart';
import 'package:gopal/core/widgets/loading.dart';
import 'package:gopal/features/usecases/players/video/index.dart';
import 'package:gopal/features/widgets/general_componenets/svg_icon.dart';

import 'controller.dart';

class VideoPreviewPage extends GetView<VideoPreviewPageController> {
  const VideoPreviewPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: StyleRepo.black,
      body: SafeArea(
        child: Stack(
          children: [
            Obx(
              () =>
                  !controller.isLoaded
                      ? Container(
                        color: StyleRepo.white,
                        child: const Center(child: LoadingWidget()),
                      )
                      : Column(
                        children: [
                          Expanded(
                            child: VideoPlayerWidget(
                              video: controller.video,
                              autoPlay: false,
                              withAppBar: false,
                            ),
                          ),
                          Padding(
                            padding: const EdgeInsets.fromLTRB(16, 34, 16, 16),
                            child: Row(
                              children: [
                                Expanded(
                                  child: Container(
                                    decoration: BoxDecoration(
                                      color: StyleRepo.white,
                                      boxShadow: StyleRepo.elevation_2,
                                      borderRadius: BorderRadius.circular(12),
                                    ),
                                    child: TextField(
                                      controller: controller.textController,
                                      minLines: 1,
                                      maxLines: 3,
                                      decoration: InputDecoration(
                                        hintText: LocaleKeys.type_message.tr(),
                                        border: OutlineInputBorder(
                                          borderSide: BorderSide.none,
                                          borderRadius: BorderRadius.circular(
                                            12,
                                          ),
                                        ),
                                        enabledBorder: OutlineInputBorder(
                                          borderSide: BorderSide.none,
                                          borderRadius: BorderRadius.circular(
                                            12,
                                          ),
                                        ),
                                        focusedBorder: OutlineInputBorder(
                                          borderSide: BorderSide.none,
                                          borderRadius: BorderRadius.circular(
                                            12,
                                          ),
                                        ),
                                      ),
                                    ),
                                  ),
                                ),
                                const Gap(12),
                                GestureDetector(
                                  onTap: () => controller.sendVideo(),
                                  child: Container(
                                    padding: const EdgeInsets.all(8),
                                    decoration: const BoxDecoration(
                                      color: StyleRepo.turquoise,
                                      shape: BoxShape.circle,
                                    ),
                                    child: SvgIcon(
                                      Assets.icons.send.path,
                                      color: StyleRepo.white,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
            ),
            Align(
              alignment: AlignmentDirectional.topEnd,
              child: SafeArea(
                child: Container(
                  margin: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: StyleRepo.black.withValues(alpha: .25),
                    shape: BoxShape.circle,
                  ),
                  child: IconButton(
                    onPressed: () => Get.back(),
                    icon: const Icon(Icons.close, color: StyleRepo.white),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
