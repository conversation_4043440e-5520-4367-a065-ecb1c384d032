import 'package:easy_localization/easy_localization.dart' hide TextDirection;
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart' hide Trans;
import 'package:gopal/core/localization/strings.dart';
import 'package:gopal/core/style/assets/gen/assets.gen.dart';
import 'package:gopal/core/style/repo.dart';
import 'package:gopal/features/widgets/general_componenets/buttons/elevated_button.dart';

import '../../widgets/backgrounds/auth_bg.dart';
import '../widgets/auth_card.dart';
import 'controller.dart';
import 'widgets/code_field.dart';
import 'widgets/code_timer.dart';

class VerificationPage extends GetView<VerificationPageController> {
  const VerificationPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      // resizeToAvoidBottomInset: false,
      body: AuthBackground(
        backgroundImage: Assets.images.authBackgroundTest2,
        child: Safe<PERSON>rea(
          child: Column(
            children: [
              const Align(
                alignment: AlignmentDirectional.centerStart,
                child: Padding(
                  padding: EdgeInsets.symmetric(vertical: 16),
                  child: BackButton(),
                ),
              ),
              Gap(
                MediaQuery.of(context).viewInsets.bottom > 0
                    ? 30
                    : Get.height * .15,
              ),
              Expanded(
                child: SingleChildScrollView(
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  child: AuthCard(
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Assets.images.logo.image(
                          width: MediaQuery.sizeOf(context).width * .25,
                        ),
                        const Gap(20),
                        Text(
                          LocaleKeys.verify_phone_number.tr(),
                          style: context.textTheme.titleLarge!.copyWith(
                            color: StyleRepo.berkeleyBlue,
                          ),
                        ),
                        const Gap(10),
                        RichText(
                          text: TextSpan(
                            children: [
                              TextSpan(
                                text: LocaleKeys.enter_the_code_sent_to.tr(),
                                style: context.textTheme.bodyMedium!.copyWith(
                                  color: StyleRepo.grey.shade700,
                                ),
                              ),
                              const WidgetSpan(child: SizedBox(width: 6)),
                              TextSpan(
                                text: controller.nav.phone,
                                style: context.textTheme.bodyMedium!.copyWith(
                                  color: StyleRepo.blueViolet,
                                  decoration: TextDecoration.underline,
                                ),
                              ),
                            ],
                          ),
                        ),
                        const Gap(28),
                        const CodeField(),
                        const Gap(28),
                        Obx(
                          () => AppElevatedButton(
                            onTap: () async => await controller.verify(),
                            enabled: controller.canVerify,
                            child: Text(LocaleKeys.confirm.tr()),
                          ),
                        ),
                        const Gap(28),
                        const CodeTimer(),
                      ],
                    ),
                  ),
                ),
              ),
              // const Gap(50),
            ],
          ),
        ),
      ),
    );
  }
}
