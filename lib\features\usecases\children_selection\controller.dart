// ignore_for_file: invalid_use_of_protected_member

import 'package:get/get.dart';
import 'package:gopal/core/models/user/main_child.dart';
import 'package:gopal/core/services/state_management/observable_variable.dart';

import '../../../core/services/rest_api/rest_api.dart';

class ChildrenSelectionController extends GetxController {
  final int? activityId;
  final List<int> initialSelected;
  final bool isMultiSelect;

  ChildrenSelectionController({
    this.activityId,
    this.initialSelected = const [],
    required this.isMultiSelect,
  }) {
    selectedChildren.addAll(initialSelected);
  }

  RxSet<int> selectedChildren = RxSet();

  final Rx<bool> _isAllSelected = false.obs;
  bool get isAllSelected => _isAllSelected.value;
  set isAllSelected(bool value) => _isAllSelected.value = value;

  add$RemoveChild(int id) {
    if (selectedChildren.contains(id)) {
      selectedChildren.remove(id);
      if (isAllSelected) {
        isAllSelected = false;
      }
    } else {
      if (!isMultiSelect && selectedChildren.isNotEmpty) {
        add$RemoveChild(selectedChildren.first);
      }
      selectedChildren.add(id);

      bool allSelected = true;
      for (var child in allChildren) {
        if (!selectedChildren.contains(child.id)) {
          allSelected = false;
          break;
        }
      }

      if (allSelected) {
        isAllSelected = true;
      }
    }
  }

  selectAll() {
    if (isAllSelected) {
      isAllSelected = false;
      selectedChildren.clear();
    } else {
      isAllSelected = true;
      selectedChildren.clear();
      selectedChildren.addAll(allChildren.map((e) => e.id!).toSet());
    }
  }

  late ObsList<MainChild> children = ObsList([]);
  late Set<MainChild> allChildren;

  loadData() async {
    ResponseModel response = await APIService.instance.requestAPI(
      Request(
        endPoint: EndPoints.children,
        params:
            activityId == null
                ? null
                : {"sub_activity_id": activityId, "has_booking": 0},
        fromJson: MainChild.fromJson,
      ),
    );
    if (response.success) {
      allChildren = (response.data as List<MainChild>).toSet();
      children.value = response.data;
    } else {
      children.error = response.message;
    }
  }

  refreshData() {
    children.reset();
    loadData();
  }

  @override
  void onInit() {
    loadData();
    super.onInit();
  }
}
