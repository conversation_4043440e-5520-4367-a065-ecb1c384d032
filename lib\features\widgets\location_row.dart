import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart';
import 'package:gopal/core/services/location.dart';
import 'package:gopal/core/style/repo.dart';
import 'package:gopal/features/widgets/general_componenets/svg_icon.dart';

import '../center/profile/models/center_details.dart';

class LocationRow extends StatelessWidget {
  final CenterBranch branch;
  const LocationRow({super.key, required this.branch});

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: () => LocationUtils.openMapApp(branch.lat, branch.lng),
      child: Row(
        children: [
          SvgIcon(
            Assets.icons.location.path,
            color: StyleRepo.grey,
          ),
          const Gap(8),
          Expanded(
            child: Text(
              branch.cityName +
                  (branch.name != null ? " - ${branch.name}" : ""),
              maxLines: 1,
              style: context.textTheme.bodySmall!.copyWith(
                color: StyleRepo.grey,
                decoration: TextDecoration.underline,
                decorationColor: StyleRepo.blueViolet,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
