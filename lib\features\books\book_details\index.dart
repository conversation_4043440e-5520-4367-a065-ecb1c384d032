import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart' hide Trans;
import 'package:gopal/core/localization/strings.dart';
import 'package:gopal/core/models/book/book.dart';
import 'package:gopal/core/routes.dart';
import 'package:gopal/core/services/state_management/widgets/obs_widget.dart';
import 'package:gopal/core/style/repo.dart';
import 'package:gopal/core/widgets/image.dart';
import 'package:gopal/features/widgets/general_componenets/buttons/outlined_button.dart';
import 'package:gopal/features/widgets/general_componenets/pages/general_page.dart';

import '../../widgets/general_componenets/qr_template.dart';
import 'controller.dart';
import 'widgets/date_time_info.dart';
import 'widgets/header.dart';

class BookDetailsPage extends StatelessWidget {
  final int id;
  const BookDetailsPage({super.key, required this.id});

  @override
  Widget build(BuildContext context) {
    // ignore: unused_local_variable
    BookDetailsPageController controller = Get.put(
      BookDetailsPageController(id),
    );
    return GeneralPage(
      titleText: LocaleKeys.book_details.tr(),
      withBackground: true,
      child: ObsVariableBuilder(
        obs: controller.book,
        onRefresh: () async => await controller.refreshData(),
        builder: (context, book) {
          return ListView(
            padding: const EdgeInsets.only(top: 24),
            children: [
              BookDetailsHeader(book: book),
              const Gap(16),
              Container(
                width: double.infinity,
                margin: const EdgeInsets.symmetric(horizontal: 16),
                padding: const EdgeInsets.fromLTRB(12, 12, 12, 12),
                decoration: BoxDecoration(
                  color: StyleRepo.white,
                  borderRadius: BorderRadius.circular(20),
                  boxShadow: StyleRepo.elevation_3,
                ),
                child: Row(
                  children: [
                    AppImage(
                      path: book.child.image.small,
                      type: ImageType.CachedNetwork,
                      height: 50,
                      width: 50,
                      decoration: const BoxDecoration(shape: BoxShape.circle),
                    ),
                    const Gap(12),
                    Expanded(
                      child: Text(
                        "${book.child.firstName} ${book.child.lastName}",
                        style: context.textTheme.titleLarge!.copyWith(
                          fontWeight: FontWeight.w600,
                          color: StyleRepo.berkeleyBlue,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              const Gap(16),
              BookDateTimeInfo(book: book),
              const Gap(16),
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16),
                child: AppOutlinedButton(
                  onTap:
                      () => Nav.to(
                        Pages.activity_details,
                        arguments: book.activity.id,
                        preventDuplicates: false,
                      ),
                  child: Text(LocaleKeys.activity_details.tr()),
                ),
              ),
              const Gap(16),
              if (book.qrs.isNotEmpty) QrCodesList(qrs: book.qrs),
              const Gap(16),
            ],
          );
        },
      ),
    );
  }
}

class QrCodesList extends StatelessWidget {
  final List<QrCodeData> qrs;
  const QrCodesList({super.key, required this.qrs});

  @override
  Widget build(BuildContext context) {
    return Column(
      spacing: 12,
      children:
          qrs.map((qrCode) {
            return Center(
              child: Container(
                decoration: BoxDecoration(
                  color: StyleRepo.white,
                  borderRadius: BorderRadius.circular(8),
                  boxShadow: StyleRepo.elevation_3,
                ),
                child: Opacity(
                  opacity: qrCode.scanned ? .5 : 1,
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(8),
                    child: QRTemplate(
                      content: qrCode.content,
                      size: 200,
                      squaresColor:
                          qrCode.scanned ? StyleRepo.grey : StyleRepo.black,
                    ),
                  ),
                ),
              ),
            );
          }).toList(),
    );
  }
}
