import 'dart:developer';
import 'dart:isolate';

import 'package:firebase_crashlytics/firebase_crashlytics.dart';
import 'package:flutter/foundation.dart';

import '../models/exceptions.dart';

abstract class Parser {
  static dynamic parsingData<T>(
    var data,
    T Function(Map<String, dynamic> json) fromJson,
  ) {
    if (data is List) {
      List<T> parsed = [];
      try {
        for (var json in data) {
          parsed.add(fromJson(json));
        }
        log(parsed.runtimeType.toString());
        return parsed;
      } on TypeError catch (e) {
        String error =
            "${e.toString()}\n${e.stackTrace!.toString().split('\n').first}";
        log(error, name: "PARSER");

        if (!kDebugMode) {
          FirebaseCrashlytics.instance.recordError(e, e.stackTrace);
        }

        throw ModellingException(error);
      } catch (e) {
        rethrow;
      }
    } else if (data is Map<String, dynamic>) {
      try {
        return from<PERSON>son(data);
      } on TypeError catch (e) {
        String error =
            "${e.toString()}\n${e.stackTrace!.toString().split('\n').first.replaceAll("#0", "").trim()}";
        log(error, name: "PARSER");

        if (!kDebugMode) {
          FirebaseCrashlytics.instance.recordError(e, e.stackTrace);
        }

        throw ModellingException(error);
      } catch (e) {
        rethrow;
      }
    } else {
      return null;
    }
  }

  static Future<dynamic> parsingDataIsolate<T>(
    var data,
    T Function(Map<String, dynamic> json) fromJson,
  ) async {
    return await Isolate.run(() => parsingData<T>(data, fromJson));
  }
}
