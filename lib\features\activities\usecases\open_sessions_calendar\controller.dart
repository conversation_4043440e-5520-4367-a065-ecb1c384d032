// ignore_for_file: invalid_use_of_protected_member

import 'package:get/get.dart';
import 'package:gopal/core/models/activity/session.dart';
import 'package:gopal/core/services/rest_api/rest_api.dart';
import 'package:gopal/core/services/state_management/observable_variable.dart';

import '../booking/booking.dart';

class OpenSessionsController extends GetxController with Booking {
  final int id;
  late final DateTime minDate, maxDate;

  OpenSessionsController({
    required this.id,
    required DateTime minDate,
    required DateTime maxDate,
  }) {
    final now = DateTime.now();
    this.minDate = minDate.isBefore(now) ? now : minDate;
    this.maxDate = maxDate.isBefore(now) ? now : maxDate;
  }
  //SECTION - Calendar
  ObsVar<Map<int, List<ActivitySession>>> calendarSessions = ObsVar(null);

  final Rx<DateTime> _selectedDate = DateTime.now().obs;
  DateTime get selectedDate => _selectedDate.value;
  set selectedDate(DateTime value) {
    if (value.isBefore(minDate)) {
      _selectedDate.value = minDate;
    } else {
      _selectedDate.value = value;
    }
    selectedOpenSession = -1;
    if (value.month != _selectedDate.value.month) {
      onMonthChanged(value, withSelectionDate: false);
    }
  }

  onMonthChanged(DateTime date, {bool withSelectionDate = true}) {
    if (withSelectionDate) {
      _selectedDate.value = date;
      selectedOpenSession = -1;
    }
    loadOpenSessions(date.year, date.month);
  }

  refreshCalendar() {
    onMonthChanged(selectedDate, withSelectionDate: false);
  }

  RxSet<int> highlightedDays = RxSet();

  //!SECTION

  //SECTION - open sessions
  Request<ActivitySession>? openSessionsRequest;

  loadOpenSessions(int year, int month) async {
    calendarSessions.reset();
    openSessionsRequest?.stop();
    highlightedDays.value = {};

    openSessionsRequest = Request(
      endPoint: EndPoints.session_values,
      params: {
        "sub_activity_id": id,
        "year": selectedDate.year,
        "month": selectedDate.month,
      },
      fromJson: ActivitySession.fromJson,
    );

    ResponseModel response = await APIService.instance.requestAPI(
      openSessionsRequest!,
    );
    if (response.success) {
      List<ActivitySession> temp = response.data;
      Map<int, List<ActivitySession>> map = {};
      for (var session in temp) {
        if (map[session.date.day] == null) {
          map[session.date.day] = [];
        }
        map[session.date.day]!.add(session);
      }
      highlightedDays.value = map.keys.toSet();
      calendarSessions.value = map;
      selectedDate = DateTime(
        selectedDate.year,
        selectedDate.month,
        calendarSessions.value!.keys.isEmpty
            ? 1
            : calendarSessions.value!.keys.first,
      );
    } else if (response.errorType is! CANCELED) {
      calendarSessions.error = response.message;
    }
  }

  final Rx<int> _selectedOpenSession = (-1).obs;
  int get selectedOpenSession => _selectedOpenSession.value;
  set selectedOpenSession(int value) => _selectedOpenSession.value = value;

  //!SECTION

  @override
  void onInit() {
    selectedDate = minDate.isAfter(DateTime.now()) ? minDate : DateTime.now();
    loadOpenSessions(selectedDate.year, selectedDate.month);
    super.onInit();
  }
}
