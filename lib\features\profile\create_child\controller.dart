import 'dart:developer';

import 'package:dio/dio.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart' hide Trans, FormData, MultipartFile;
import 'package:gopal/core/localization/strings.dart';
import 'package:gopal/core/models/constants/gender.dart';
import 'package:gopal/core/models/selection.dart';
import 'package:gopal/core/models/user/main_child.dart';
import 'package:gopal/core/services/state_management/observable_variable.dart';
import 'package:gopal/core/utils/image_utils.dart';
import 'package:gopal/features/profile/create_child/models/child_creation.dart';
import 'package:gopal/features/usecases/gender/choose_gender.dart';
import 'package:gopal/features/usecases/toast/toast.dart';
import 'package:multi_dropdown/multiselect_dropdown.dart' hide RequestMethod;

import '../../../core/services/rest_api/rest_api.dart';
import 'models/nav.dart';

class CreateChildPageController extends GetxController {
  final CreateChildPageNav nav;
  CreateChildPageController(this.nav) {
    if (nav.child != null) {
      ChildCreation data = nav.child!;
      log(data.toJson().toString());
      image = data.image ?? "";
      firstName.text = data.firstName;
      lastName.text = data.lastName;
      gender.value = data.gender;
      selectBirthDate(data.birthDate);
      initHobbies = data.hobbies;
      allergies.text = data.allergy ?? "";
      initSpecialCases = data.specialCases;
      editMode = true;
    }
  }

  final form = GlobalKey<FormState>();
  //SECTION - Data
  bool editMode = false;
  TextEditingController firstName = TextEditingController();
  TextEditingController lastName = TextEditingController();
  TextEditingController allergies = TextEditingController();

  final Rx<String> _image = "".obs;
  String get image => _image.value;
  set image(String value) => _image.value = value;

  DateTime? birthDate;
  TextEditingController birthDateController = TextEditingController();
  selectBirthDate(DateTime date) {
    birthDate = date;
    birthDateController.text = DateFormat.yMd(
      EasyLocalization.of(Get.context!)!.currentLocale!.languageCode,
    ).format(birthDate!);
  }

  ObsVar<Gender> gender = ObsVar(null);

  List<Selection> initHobbies = [];
  MultiSelectController<Selection> hobbiesController =
      MultiSelectController<Selection>();

  List<Selection> initSpecialCases = [];
  MultiSelectController<Selection> specialCasesController =
      MultiSelectController<Selection>();

  //!SECTION

  //SECTION - init values for Edition
  @override
  void onInit() {
    fetchHobbies();
    fetchSpecialCases();
    super.onInit();
  }
  //!SECTION

  //SECTION - Data entry

  Future<String?> pickImage() async {
    String? result = await ImageUtils.pickAndCompressImage();
    if (result != null) {
      image = result;
    }
    return result;
  }

  pickDate(context) async {
    DateTime? pickedDate = await showDatePicker(
      context: context,
      firstDate: DateTime(2000),
      lastDate: DateTime.now().subtract(2.days),
      initialDate: birthDate,
    );
    if (pickedDate != null) {
      selectBirthDate(pickedDate);
    }
  }

  Future<Gender?> pickGender() async {
    Gender? pickedGender = await ChooseGender.show(
      initialValue: gender.value,
      title: LocaleKeys.please_select_child_gender.tr(),
    );
    if (pickedGender != null) {
      gender.value = pickedGender;
    }
    return pickedGender;
  }
  //!SECTION

  //SECTION - Fetch Data
  //SECTION - Hobbies
  ObsList<Selection> hobbies = ObsList([]);
  fetchHobbies() async {
    ResponseModel response = await APIService.instance.requestAPI(
      Request<Selection>(
        endPoint: EndPoints.hobbies,
        fromJson: Selection.fromJson,
      ),
    );
    if (response.success) {
      hobbiesController.setOptions(
        (response.data as List<Selection>)
            .map((e) => ValueItem<Selection>(label: e.name, value: e))
            .toList(),
      );
      hobbiesController.setSelectedOptions(
        hobbiesController.options
            .where((e) => initHobbies.contains(e.value))
            .toList(),
      );
      hobbies.value = response.data;
    } else {
      hobbies.error = response.message;
    }
  }

  refreshHobbies() async {
    hobbies.reset();
    await fetchHobbies();
  }
  //!SECTION

  ObsList<Selection> specialCases = ObsList([]);
  fetchSpecialCases() async {
    ResponseModel response = await APIService.instance.requestAPI(
      Request<Selection>(
        endPoint: EndPoints.special_cases,
        fromJson: Selection.fromJson,
      ),
    );
    if (response.success) {
      specialCasesController.setOptions(
        (response.data as List<Selection>)
            .map((e) => ValueItem<Selection>(label: e.name, value: e))
            .toList(),
      );
      specialCasesController.setSelectedOptions(
        specialCasesController.options
            .where((e) => initSpecialCases.contains(e.value))
            .toList(),
      );
      specialCases.value = response.data;
    } else {
      specialCases.error = response.message;
    }
  }

  refreshSpecialCases() async {
    specialCases.reset();
    await fetchSpecialCases();
  }
  //!SECTION

  confirm() async {
    if (!form.currentState!.validate()) {
      return;
    }
    ChildCreation child = ChildCreation(
      id: nav.child?.id,
      image: image,
      firstName: firstName.text,
      lastName: lastName.text,
      birthDate: birthDate!,
      gender: gender.value!,
      hobbies: hobbiesController.selectedOptions.map((e) => e.value!).toList(),
      allergy: allergies.text,
      specialCases:
          specialCasesController.selectedOptions.map((e) => e.value!).toList(),
    );
    if (nav.withAPI) {
      FormData data = FormData.fromMap({});
      await child.addToForm(data, 0);
      ResponseModel response = await APIService.instance.requestAPI(
        Request(
          endPoint: EndPoints.update_profile,
          method: RequestMethod.Post,
          body: data,
        ),
      );
      if (response.success) {
        Get.back(
          result: MainChild.fromJson(
            (response.data['children'] as List).firstWhereOrNull(
                  (element) => element['id'] == child.id,
                ) ??
                response.data['children'].last,
          ),
        );
        return;
      } else {
        Toast.show(message: response.message);
        return;
      }
    }
    log(child.toJson().toString());
    Get.back(result: child);
  }

  @override
  void onClose() {
    firstName.dispose();
    lastName.dispose();
    birthDateController.dispose();
    allergies.dispose();
    super.onClose();
  }
}
