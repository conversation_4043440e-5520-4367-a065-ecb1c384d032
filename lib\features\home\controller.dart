import 'dart:developer';

import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart' hide Trans;
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:gopal/core/config/app_builder.dart';
import 'package:gopal/core/config/role.dart';
import 'package:gopal/core/localization/strings.dart';
import 'package:gopal/core/models/category.dart';
import 'package:gopal/core/models/user/center.dart';
import 'package:gopal/core/routes.dart';
import 'package:gopal/core/services/location.dart';
import 'package:gopal/core/services/state_management/observable_variable.dart';
import 'package:gopal/core/widgets/loading.dart';
import 'package:gopal/features/center/profile/models/nav.dart';
import 'package:gopal/features/home/<USER>/ad.dart';
import 'package:hotspot/hotspot.dart';
import 'package:url_launcher/url_launcher.dart';

import '../../core/models/activity/main_activity.dart';
import '../../core/services/rest_api/rest_api.dart';
import '../activities/activities/models/nav.dart';

class HomePageController extends GetxController {
  AppBuilder appBuilder = Get.find();

  GlobalKey hotspotKey = GlobalKey();
  GlobalKey todaysOffersKey = GlobalKey();
  GlobalKey centersKey = GlobalKey();
  GlobalKey seasonalActivitiesKey = GlobalKey();
  GlobalKey familyActivitiesKey = GlobalKey();

  ScrollController scrollController = ScrollController();
  //SECTION - Categories
  ObsList<Category> categories = ObsList([]);

  Future fetchCategories() async {
    ResponseModel response = await APIService.instance.requestAPI(
      Request(endPoint: EndPoints.categories, fromJson: Category.fromJson),
    );
    if (response.success) {
      categories.value = response.data;
    } else {
      categories.error = response.message;
    }
  }

  Future refreshCategories() async {
    categories.reset();
    await fetchCategories();
  }
  //!SECTION

  //SECTION - Slider

  final Rx<int> _currentImage = 0.obs;
  int get currentImage => _currentImage.value;
  set currentImage(int value) => _currentImage.value = value;

  ObsList<SliderAd> adsRequest = ObsList([]);

  ObsList<SliderAd> ads = ObsList([]);

  Future fetchAds() async {
    ResponseModel response = await APIService.instance.requestAPI(
      Request(endPoint: EndPoints.ads, fromJson: SliderAd.fromJson),
    );
    if (response.success) {
      ads.value = response.data;
    } else {
      ads.error = response.message;
    }
  }

  Future refreshAds() async {
    ads.reset();
    await fetchAds();
  }

  adClick(SliderAd ad) {
    switch (ad.actionType) {
      case AdActionType.internal:
        switch (ad.internalType!) {
          case InternalAdType.activity:
            return Nav.to(
              Pages.activity_details,
              arguments: ad.adableId,
              preventDuplicates: false,
            );
          case InternalAdType.provider:
            return Nav.to(
              Pages.center,
              arguments: CenterProfilePageNav(ad.adableId!),
              preventDuplicates: false,
            );
        }
      case AdActionType.external:
        launchUrl(Uri.parse(ad.link!), mode: LaunchMode.externalApplication);
        return;
      default:
        return;
    }
  }
  //!SECTION

  //SECTION - centers
  ObsList<CenterModel> centers = ObsList([]);

  Future fetchCenters() async {
    LatLng? result = await LocationUtils.getMyLocation(openSettings: true);

    ResponseModel response = await APIService.instance.requestAPI(
      Request(
        endPoint: EndPoints.centers,
        params:
            result == null
                ? null
                : {"lat": result.latitude, "lng": result.longitude},
        fromJson: CenterModel.fromJson,
      ),
    );
    if (response.success) {
      (response.data as List<CenterModel>).sort(
        (a, b) => a.distance!.compareTo(b.distance!),
      );
      centers.value = response.data;
    } else {
      centers.error = response.message;
    }
  }

  Future refreshCenters() async {
    centers.reset();
    await fetchCenters();
  }
  //!SECTION

  //SECTION - Activities
  ObsList<MainActivity> familyActivities = ObsList([]);

  Future fetchFamilyActivities() async {
    ResponseModel response = await APIService.instance.requestAPI(
      Request(
        endPoint: EndPoints.main_activities,
        params: {"is_family": 1},
        fromJson: MainActivity.fromJson,
      ),
    );
    if (response.success) {
      familyActivities.value = response.data;
    } else {
      familyActivities.error = response.message;
    }
  }

  Future refreshFamily() async {
    familyActivities.reset();
    await fetchFamilyActivities();
  }

  ObsList<MainActivity> offeredActivities = ObsList([]);

  Future fetchOfferedActivities() async {
    ResponseModel response = await APIService.instance.requestAPI(
      Request(
        endPoint: EndPoints.main_activities,
        params: {"is_discounted": 1},
        fromJson: MainActivity.fromJson,
      ),
    );
    if (response.success) {
      offeredActivities.value = response.data;
    } else {
      offeredActivities.error = response.message;
    }
  }

  Future refreshOfferedActivities() async {
    offeredActivities.reset();
    await fetchOfferedActivities();
  }

  ObsList<MainActivity> seasonalActivities = ObsList([]);

  Future fetchSeasonalActivities() async {
    ResponseModel response = await APIService.instance.requestAPI(
      Request(
        endPoint: EndPoints.main_activities,
        params: {"in_season": 1},
        fromJson: MainActivity.fromJson,
      ),
    );
    if (response.success) {
      seasonalActivities.value = response.data;
    } else {
      seasonalActivities.error = response.message;
    }
  }

  Future refreshSeasonalActivities() async {
    seasonalActivities.reset();
    await fetchSeasonalActivities();
  }

  ObsList<MainActivity> recommendedActivities = ObsList([]);

  Future fetchRecommendedActivities() async {
    ResponseModel response = await APIService.instance.requestAPI(
      Request(
        endPoint: EndPoints.main_activities,
        params: {"recommended": 1},
        fromJson: MainActivity.fromJson,
      ),
    );
    if (response.success) {
      recommendedActivities.value = response.data;
    } else {
      recommendedActivities.error = response.message;
    }
  }

  Future refreshRecommendedActivities() async {
    recommendedActivities.reset();
    await fetchRecommendedActivities();
  }
  //!SECTION

  //SECTION - Fetch and Refresh
  @override
  void onInit() {
    if (appBuilder.role is NewGuestUser) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        Loading.show();
      });
    }
    Future.wait([
      fetchAds(),
      fetchCategories(),
      fetchRecommendedActivities(),
      fetchCenters(),
      fetchFamilyActivities(),
      fetchOfferedActivities(),
      fetchSeasonalActivities(),
    ]).then((value) {
      if (appBuilder.role is NewGuestUser) {
        WidgetsBinding.instance.addPostFrameCallback((_) {
          Loading.dispose();
          HotspotProvider.of(hotspotKey.currentContext!).startFlow();
        });
      }
    });

    super.onInit();
  }

  refreshData() async {
    await Future.wait([
      refreshCategories(),
      refreshAds(),
      refreshCenters(),
      refreshFamily(),
      refreshOfferedActivities(),
      refreshSeasonalActivities(),
      refreshRecommendedActivities(),
    ]);
  }

  refreshWhenError() {
    if (categories.hasError) {
      log("Refresh categories", name: "Refresh tracking");
      refreshCategories();
    }
    if (ads.hasError) {
      log("Refresh ads", name: "Refresh tracking");
      refreshAds();
    }
    if (centers.hasError) {
      log("Refresh centers", name: "Refresh tracking");
      refreshCenters();
    }
    if (familyActivities.hasError) {
      log("Refresh families activities", name: "Refresh tracking");
      refreshFamily();
    }
    if (offeredActivities.hasError) {
      log("Refresh offered activities", name: "Refresh tracking");
      refreshOfferedActivities();
    }
    if (seasonalActivities.hasError) {
      log("Refresh seasonal activities", name: "Refresh tracking");
      refreshSeasonalActivities();
    }
    if (recommendedActivities.hasError) {
      log("Refresh recommended activities", name: "Refresh tracking");
      refreshRecommendedActivities();
    }
  }
  //!SECTION

  //SECTION - Nav
  seeAllFamilyActivities() {
    Nav.to(
      Pages.activities,
      arguments: ActivitiesPageNav(
        title: LocaleKeys.family_activities.tr(),
        endPoint: EndPoints.main_activities,
        params: {"is_family": 1},
      ),
    );
  }

  seeAllTodaysOffersActivities() {
    Nav.to(
      Pages.activities,
      arguments: ActivitiesPageNav(
        title: LocaleKeys.todays_offers.tr(),
        endPoint: EndPoints.main_activities,
        params: {"is_discounted": 1},
      ),
    );
  }

  seeAllSeasonalActivities() {
    Nav.to(
      Pages.activities,
      arguments: ActivitiesPageNav(
        title: LocaleKeys.seasonal_activities.tr(),
        endPoint: EndPoints.main_activities,
        params: {"in_season": 1},
      ),
    );
  }

  seeAllRecommendedActivities() {
    Nav.to(
      Pages.activities,
      arguments: ActivitiesPageNav(
        title: LocaleKeys.recommended_activities.tr(),
        endPoint: EndPoints.main_activities,
        params: {"recommended": 1},
      ),
    );
  }

  //!SECTION
}
