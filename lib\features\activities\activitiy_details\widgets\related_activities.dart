import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:gopal/core/localization/strings.dart';
import 'package:gopal/core/services/state_management/widgets/obs_widget.dart';
import 'package:gopal/features/widgets/horizontal_list.dart';

import '../../widgets/activities_list.dart';
import '../controller.dart';

class RelatedActivities extends StatelessWidget {
  const RelatedActivities({super.key, required this.controller});

  final ActivityDetailsPageController controller;

  @override
  Widget build(BuildContext context) {
    return ObsListBuilder(
      obs: controller.relatedActivities,
      loader:
          (_) =>
              HorizontalListLoading(title: LocaleKeys.related_activities.tr()),
      errorBuilder:
          (_, error) => HorizontalListError(
            title: LocaleKeys.related_activities.tr(),
            error: error,
          ),
      builder: (context, activities) {
        if (activities.isEmpty) {
          return const SizedBox();
        }
        return ActivitiesList(
          title: LocaleKeys.related_activities.tr(),
          activities: activities,
        );
      },
    );
  }
}
