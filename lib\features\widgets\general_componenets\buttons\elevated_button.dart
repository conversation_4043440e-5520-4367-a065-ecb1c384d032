// ignore_for_file: must_be_immutable

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:gopal/core/style/repo.dart';

const Duration _kAnimationDuration = Duration(milliseconds: 300);

class AppElevatedButton extends StatefulWidget {
  final Widget child;
  final Function()? onPressed;
  final double height;
  final double? width;
  final double horizontalPadding;

  const AppElevatedButton({
    super.key,
    required this.onPressed,
    required this.child,
    this.height = 50,
    this.horizontalPadding = 8,
    this.width = double.infinity,
  });

  @override
  State<AppElevatedButton> createState() => _AppElevatedButtonState();
}

class _AppElevatedButtonState extends State<AppElevatedButton> {
  late bool loading;

  @override
  void initState() {
    loading = false;
    super.initState();
  }

  press() async {
    if (widget.onPressed == null) return;
    if (widget.onPressed is! Future Function()) {
      return widget.onPressed!();
    }

    setState(() {
      loading = true;
    });

    try {
      await (widget.onPressed as Future Function()).call();
    } catch (_) {}

    setState(() {
      loading = false;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Align(
      child: InkWell(
        borderRadius: BorderRadius.circular(widget.height / 2),
        onTap:
            loading
                ? () {}
                : widget.onPressed == null
                ? null
                : press,
        child: AnimatedOpacity(
          duration: 300.milliseconds,
          opacity: widget.onPressed != null ? 1 : .5,
          child: AnimatedContainer(
            duration: _kAnimationDuration,
            padding: EdgeInsets.symmetric(horizontal: widget.horizontalPadding),
            height: widget.height,
            width: widget.width,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(widget.height / 2),
              gradient: const LinearGradient(
                colors: [StyleRepo.amythest, StyleRepo.blueViolet],
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
              ),
            ),
            alignment: Alignment.center,
            child: AnimatedSwitcher(
              duration: 300.milliseconds,
              transitionBuilder: (child, animation) {
                return FadeTransition(opacity: animation, child: child);
              },
              child:
                  loading
                      ? const SizedBox(
                        height: 20,
                        width: 20,
                        child: CircularProgressIndicator(
                          strokeWidth: 3,
                          color: StyleRepo.white,
                        ),
                      )
                      : IconTheme(
                        data: const IconThemeData(color: StyleRepo.white),
                        child: DefaultTextStyle(
                          style: context.textTheme.titleMedium!.copyWith(
                            color: StyleRepo.white,
                          ),
                          child: widget.child,
                        ),
                      ),
            ),
          ),
        ),
      ),
    );
  }
}
