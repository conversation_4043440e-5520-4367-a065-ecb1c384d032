import 'package:easy_localization/easy_localization.dart' hide TextDirection;
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart';
import 'package:gopal/core/localization/strings.dart';
import 'package:gopal/core/style/assets/gen/assets.gen.dart';
import 'package:gopal/core/style/repo.dart';
import 'package:gopal/features/widgets/general_componenets/app_bars/general_app_bar.dart';
import 'package:gopal/features/widgets/general_componenets/qr_template.dart';

import 'controller.dart';

class MemberCardPage extends GetView<MemberCardPageController> {
  const MemberCardPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: GeneralAppBar(title: Text(tr(LocaleKeys.member_card))),
      body: Directionality(
        textDirection: TextDirection.ltr,
        child: Center(
          child: Padding(
            padding: const EdgeInsets.all(24),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(10),
              child: AspectRatio(
                aspectRatio: .7,
                child: LayoutBuilder(
                  builder: (context, constraints) {
                    return Stack(
                      children: [
                        Assets.images.memberCardBackground.image(),
                        Positioned(
                          top: 12,
                          left: 16,
                          child: Text(
                            controller.profile.name,
                            style: TextStyle(
                              color: StyleRepo.white,
                              fontSize: constraints.maxWidth * .08,
                            ),
                          ),
                        ),
                        Center(
                          child: Container(
                            decoration: BoxDecoration(
                              color: StyleRepo.white,
                              borderRadius: BorderRadius.circular(10),
                            ),
                            child: QRTemplate(
                              content: "user-${controller.profile.uuid}",
                              size: constraints.minWidth * .4,
                            ),
                          ),
                        ),
                        Positioned(
                          bottom: 12,
                          left: 24,
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                "Member Card",
                                style: TextStyle(
                                  color: StyleRepo.white,
                                  fontSize: constraints.maxWidth * .05,
                                ),
                              ),
                              Gap(8),
                              Text(
                                "${controller.profile.children.length} Children",
                                style: TextStyle(
                                  color: StyleRepo.turquoise,
                                  fontSize: constraints.maxWidth * .04,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    );
                  },
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }
}
