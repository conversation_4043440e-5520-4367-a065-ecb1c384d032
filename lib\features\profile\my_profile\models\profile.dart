import 'package:gopal/core/models/constants/gender.dart';
import 'package:gopal/core/models/user/main_child.dart';
import 'package:gopal/core/models/user/main_user.dart';

class MyProfile extends MainUser {
  late List<MainChild> children = [];
  late int points;
  late int mainChildId;
  late String uuid;
  Gender? gender;
  DateTime? birthDate;

  MyProfile.fromJson(Map<String, dynamic> json) : super.fromJson(json) {
    children =
        (json['children'] as List)
            .map<MainChild>((e) => MainChild.fromJson(e))
            .toList();
    mainChildId = json['mainChild']['id'];
    points = json['points'] ?? 0;
    uuid = json['uuid'];
    if (json['gender'] != null) {
      gender = Gender.fromValue(json['gender']);
    }
    if (json['birthdate'] != null) {
      birthDate = DateTime.parse(json['birthdate']);
    }
  }
}
