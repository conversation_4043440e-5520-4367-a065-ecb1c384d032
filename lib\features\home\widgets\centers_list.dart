import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart' hide Trans;
import 'package:gopal/core/localization/strings.dart';
import 'package:gopal/core/models/user/center.dart';
import 'package:gopal/core/style/repo.dart';
import 'package:hotspot/hotspot.dart';

import '../../center/widgets/center_card.dart';

class CentersList extends StatelessWidget {
  final String title;
  final Function()? seeAll;
  final List<CenterModel> centers;
  const CentersList({
    super.key,
    required this.title,
    this.seeAll,
    required this.centers,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                title,
                style: context.textTheme.titleLarge!.copyWith(
                  color: StyleRepo.blueViolet,
                ),
              ),
              InkWell(
                onTap: seeAll,
                child: Padding(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 12,
                    vertical: 4,
                  ),
                  child: Row(
                    children: [
                      Text(
                        LocaleKeys.interactive_map.tr(),
                        style: context.textTheme.bodyMedium!.copyWith(
                          color: StyleRepo.blueViolet,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      const Gap(8),
                      const Icon(
                        Icons.pin_drop_outlined,
                        color: StyleRepo.blueViolet,
                      ),
                    ],
                  ),
                ),
              ).withHotspot(
                order: 6,
                title: LocaleKeys.interactive_map.tr(),
                text: LocaleKeys.interactive_map_description.tr(),
              ),
            ],
          ),
        ),
        const Gap(8),
        SizedBox(
          height: 275,
          child: ListView.separated(
            scrollDirection: Axis.horizontal,
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            itemCount: centers.length,
            separatorBuilder: (_, __) => const Gap(16),
            itemBuilder: (context, index) => CenterCard(center: centers[index]),
          ),
        ),
      ],
    );
  }
}
