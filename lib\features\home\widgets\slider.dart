import 'package:carousel_slider/carousel_slider.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart';
import 'package:gopal/core/services/state_management/widgets/obs_widget.dart';
import 'package:gopal/core/style/repo.dart';
import 'package:gopal/core/widgets/image.dart';
import 'package:gopal/core/widgets/shimmer_loading.dart';
import 'package:gopal/features/home/<USER>/ad.dart';

import '../controller.dart';

class HomeSlider extends StatelessWidget {
  const HomeSlider({super.key});

  @override
  Widget build(BuildContext context) {
    HomePageController controller = Get.find();
    return ObsListBuilder(
      obs: controller.ads,
      loader:
          (context) => AspectRatio(
            aspectRatio: 16 / 9,
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 20),
              child: ShimmerWidget.card(
                borderRadius: BorderRadius.circular(15),
              ),
            ),
          ),
      errorBuilder: (context, error) {
        return AspectRatio(
          aspectRatio: 16 / 9,
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 20),
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 12),
              decoration: BoxDecoration(
                color: StyleRepo.grey.shade300,
                borderRadius: BorderRadius.circular(15),
              ),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    error,
                    textAlign: TextAlign.center,
                    style: const TextStyle(
                      color: StyleRepo.red,
                      fontWeight: FontWeight.w700,
                    ),
                  ),
                  IconButton(
                    onPressed: () => controller.refreshWhenError(),
                    icon: const Icon(Icons.refresh),
                  ),
                ],
              ),
            ),
          ),
        );
      },
      builder: (context, data) {
        List ads = List.from(
          data.where((element) => element.type == AdType.image),
        );
        if (ads.isEmpty) {
          return const SizedBox();
        }
        return Column(
          children: [
            CarouselSlider.builder(
              itemCount: ads.length,
              options: CarouselOptions(
                autoPlay: true,
                aspectRatio: 2,
                enlargeCenterPage: true,
                onPageChanged: (page, _) => controller.currentImage = page,
              ),
              itemBuilder: (context, index, _) {
                return InkWell(
                  onTap: () => controller.adClick(ads[index]),
                  child: AppImage(
                    path: ads[index].cover ?? ads[index].image.large,
                    type: ImageType.CachedNetwork,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(15),
                      boxShadow: StyleRepo.elevation_3,
                    ),
                  ),
                );
              },
            ),
            const Gap(12),
            SizedBox(
              height: 12,
              child: Center(
                child: ListView.separated(
                  shrinkWrap: true,
                  physics: const NeverScrollableScrollPhysics(),
                  scrollDirection: Axis.horizontal,
                  itemCount: ads.length,
                  separatorBuilder: (_, __) => const Gap(4),
                  itemBuilder: (context, index) {
                    return AspectRatio(
                      aspectRatio: 1,
                      child: Obx(
                        () => AnimatedContainer(
                          duration: 300.milliseconds,
                          height: double.infinity,
                          width: double.infinity,
                          padding: EdgeInsets.all(
                            controller.currentImage == index ? 1 : 3,
                          ),
                          decoration: BoxDecoration(
                            shape: BoxShape.circle,
                            border:
                                controller.currentImage == index
                                    ? Border.all(color: StyleRepo.turquoise)
                                    : null,
                          ),
                          child: AnimatedContainer(
                            duration: 300.milliseconds,
                            height: double.infinity,
                            width: double.infinity,
                            decoration: BoxDecoration(
                              color:
                                  controller.currentImage == index
                                      ? StyleRepo.turquoise
                                      : StyleRepo.grey.shade300,
                              shape: BoxShape.circle,
                            ),
                          ),
                        ),
                      ),
                    );
                  },
                ),
              ),
            ),
          ],
        );
      },
    );
  }
}
