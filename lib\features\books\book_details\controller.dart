// ignore_for_file: invalid_use_of_protected_member

import 'dart:math';

import 'package:dio/dio.dart';
import 'package:get/get.dart';
import 'package:gopal/core/models/activity/constants/type.dart';
import 'package:gopal/core/models/activity/session.dart';
import 'package:gopal/core/models/book/book.dart';
import 'package:gopal/core/services/state_management/observable_variable.dart';

import '../../../core/services/rest_api/rest_api.dart';

class BookDetailsPageController extends GetxController {
  final int id;

  BookDetailsPageController(this.id);

  late ObsVar<BookedActivity> book = ObsVar(null);

  fetchBook() async {
    ResponseModel response = await APIService.instance.requestAPI(
      Request(
        endPoint: EndPoints.book_details(id),
        fromJson: BookedActivity.fromJsonTypes,
      ),
    );
    if (response.success) {
      book.value = response.data;
      if (book.value!.type == ActivityType.specific_days) {
        loadSpecificSessions(selectedDate.year, selectedDate.month);
      }
    } else {
      book.error = response.message;
    }
  }

  Future refreshData() async {
    book.reset();
    await fetchBook();
    _selectedDate.value = DateTime.now();
  }

  @override
  void onInit() {
    fetchBook();
    super.onInit();
  }

  //SECTION - Calendar
  ObsVar<Map<int, List<ActivitySession>>> calendarSessions = ObsVar(null);

  final Rx<DateTime> _selectedDate = DateTime.now().obs;
  DateTime get selectedDate => _selectedDate.value;
  set selectedDate(DateTime value) {
    _selectedDate.value = value;
    if (value.month != _selectedDate.value.month) {
      onMonthChanged(value, withSelectionDate: false);
    }
  }

  onMonthChanged(DateTime date, {bool withSelectionDate = true}) {
    if (withSelectionDate) {
      _selectedDate.value = date;
    }
    if (book.value!.type == ActivityType.specific_days) {
      loadSpecificSessions(date.year, date.month);
    }
  }

  refreshCalendar() {
    onMonthChanged(selectedDate, withSelectionDate: false);
  }

  RxSet<int> highlightedDays = RxSet();

  //!SECTION

  //SECTION - specific days sessions
  ObsList<ActivitySession>? specificSessionsRequest = ObsList([]);

  CancelToken? cancel;

  loadSpecificSessions(int year, int month) async {
    calendarSessions.reset();
    cancel?.cancel();
    specificSessionsRequest?.reset();
    highlightedDays.value = {};

    cancel = CancelToken();

    ResponseModel response = await APIService.instance.requestAPI(
      Request(
        endPoint: EndPoints.specific_session_values,
        params: {
          "sub_activity_id": book.value!.activity.id,
          "year": selectedDate.year,
          "month": selectedDate.month,
        },
        cancelToken: cancel,
      ),
    );

    cancel = null;

    if (response.success) {
      List<ActivitySession> temp =
          response.data
              .map<ActivitySession>((e) => ActivitySession.fromJson(e))
              .toList();
      Map<int, List<ActivitySession>> map = {};
      for (var session in temp) {
        if (map[session.date.day] == null) {
          map[session.date.day] = [];
        }
        map[session.date.day]!.add(session);
      }
      highlightedDays.value = map.keys.toSet();
      calendarSessions.value = map;
      int firstDay = calendarSessions.value!.keys.toList().reduce(min);
      if (firstDay < DateTime.now().day) {
        firstDay = DateTime.now().day;
      }
      selectedDate = DateTime(selectedDate.year, selectedDate.month, firstDay);
    } else if (response.errorType is CANCELED) {
      return;
    } else {
      calendarSessions.error = response.message;
    }
  }

  //!SECTION
}
