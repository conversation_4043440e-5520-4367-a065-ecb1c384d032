import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart' hide Trans;
import 'package:gopal/core/config/app_builder.dart';
import 'package:gopal/core/config/role_middleware.dart';
import 'package:gopal/core/localization/strings.dart';
import 'package:gopal/core/routes.dart';
import 'package:gopal/core/style/repo.dart';
import 'package:gopal/core/widgets/image.dart';
import 'package:gopal/features/home/<USER>';
import 'package:gopal/features/widgets/general_componenets/svg_icon.dart';
import 'package:hotspot/hotspot.dart';

import '../../usecases/confirmation/index.dart';
import 'drawer_card.dart';
import 'notification_switch.dart';

class HomeDrawer extends StatelessWidget {
  const HomeDrawer({super.key});

  @override
  Widget build(BuildContext context) {
    AppBuilder appBuilder = Get.find();
    return Drawer(
      child: Stack(
        children: [
          Positioned(bottom: 0, child: Assets.images.sideBarBackground.image()),
          Column(
            children: [
              DrawerHeader(
                child: Column(
                  children: [
                    ValueListenableBuilder(
                      valueListenable: appBuilder.user,
                      builder: (context, user, _) {
                        return AppImage(
                          path: user?.image?.small ?? "",
                          type: ImageType.CachedNetwork,
                          height: 70,
                          width: 70,
                          decoration: const BoxDecoration(
                            shape: BoxShape.circle,
                          ),
                        );
                      },
                    ),
                    const Gap(12),
                    ValueListenableBuilder(
                      valueListenable: appBuilder.user,
                      builder: (context, user, _) {
                        return Text(
                          user?.name ?? "",
                          style: context.textTheme.titleLarge!.copyWith(
                            color: StyleRepo.blueViolet,
                            fontWeight: FontWeight.w600,
                          ),
                        );
                      },
                    ),
                    ValueListenableBuilder(
                      valueListenable: appBuilder.user,
                      builder: (context, user, _) {
                        return Text(
                          user?.phone ?? "",
                          style: context.textTheme.labelMedium!.copyWith(
                            color: StyleRepo.grey,
                          ),
                        );
                      },
                    ),
                  ],
                ),
              ),
              Expanded(
                child: ListView(
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  children: [
                    DrawerCard(
                      onTap: () => Nav.to(Pages.support_replies),
                      icon: SvgIcon(Assets.icons.targettedQuestions.path),
                      title: LocaleKeys.support_replies.tr(),
                    ),
                    const Gap(8),
                    DrawerCard(
                      icon: const Icon(Icons.chat_outlined),
                      title: LocaleKeys.chats.tr(),
                      onTap: () => Nav.to(Pages.chat_rooms),
                    ),
                    const Gap(8),
                    DrawerCard(
                      icon: SvgIcon(Assets.icons.tour.path),
                      title: LocaleKeys.app_tour.tr(),
                      onTap: () {
                        Get.find<HomePageController>().scrollController
                            .animateTo(
                              0,
                              duration: 200.milliseconds,
                              curve: Curves.linear,
                            );
                        HotspotProvider.of(context).startFlow();
                      },
                    ),
                    const Gap(8),
                    DrawerCard(
                      onTap: () {
                        Get.find<AppBuilder>().updateLocale();
                        if (Get.isRegistered<HomePageController>()) {
                          Get.find<HomePageController>().refreshData();
                        }
                      },
                      icon: SvgIcon(Assets.icons.languages.path),
                      title: LocaleKeys.language.tr(),
                      trailing: Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 16,
                          vertical: 4,
                        ),
                        decoration: BoxDecoration(
                          color: StyleRepo.blueViolet,
                          borderRadius: BorderRadius.circular(45),
                        ),
                        child: Text(
                          Get.find<AppBuilder>()
                              .currentLocale
                              .locale
                              .languageCode
                              .capitalize!,
                          style: context.textTheme.labelMedium!.copyWith(
                            color: StyleRepo.white,
                          ),
                        ),
                      ),
                    ),
                    const Gap(8),
                    const NotificationSwitch(),
                    const Gap(8),
                    DrawerCard(
                      icon: SvgIcon(Assets.icons.terms.path),
                      title: LocaleKeys.terms_and_conditions.tr(),
                      onTap: () => Nav.to(Pages.terms),
                    ),
                    const Gap(8),
                    DrawerCard(
                      icon: SvgIcon(Assets.icons.about.path),
                      title: LocaleKeys.about_us.tr(),
                      onTap: () => Nav.to(Pages.about),
                    ),
                    if (!RoleMiddleware.checkIfGuest) const Gap(8),
                    if (!RoleMiddleware.checkIfGuest)
                      DrawerCard(
                        icon: SvgIcon(Assets.icons.share.path),
                        title: LocaleKeys.share.tr(),
                        onTap: () => Nav.to(Pages.invitation),
                      ),
                    const Gap(8),
                    const Gap(8),
                    DrawerCard(
                      icon: SvgIcon(Assets.icons.delete.path),
                      title: LocaleKeys.delete_account.tr(),
                      isRed: true,
                      onTap:
                          () => ConfirmationDialog.confirm(
                            onAccept:
                                () => Get.find<AppBuilder>().logout(
                                  isDelete: true,
                                ),
                            body:
                                LocaleKeys.delete_account_confirmation_message
                                    .tr(),
                          ),
                    ),
                    const Gap(8),
                    DrawerCard(
                      icon: SvgIcon(Assets.icons.delete.path),
                      title: LocaleKeys.logout.tr(),
                      isRed: true,
                      onTap:
                          () => ConfirmationDialog.confirm(
                            onAccept: () => Get.find<AppBuilder>().logout(),
                            body: LocaleKeys.logout_confirmation_message.tr(),
                          ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
