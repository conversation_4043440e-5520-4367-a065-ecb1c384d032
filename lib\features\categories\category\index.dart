import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart' hide Trans;
import 'package:gopal/core/constants/controllers_tags.dart';
import 'package:gopal/core/localization/strings.dart';
import 'package:gopal/core/models/activity/main_activity.dart';
import 'package:gopal/core/services/pagination/options/grid_view.dart';
import 'package:gopal/features/widgets/general_componenets/pages/general_page.dart';
import 'package:gopal/features/widgets/general_componenets/svg_icon.dart';

import '../../activities/widgets/activity_card.dart';
import '../../widgets/list_cards_loading.dart';
import 'controller.dart';
import 'widgets/sub_categories.dart';

class CategoryPage extends GetView<CategoryPageController> {
  const CategoryPage({super.key});

  @override
  Widget build(BuildContext context) {
    return GeneralPage(
      title: Obx(
        () => Text(
          controller.selectedCategory.isNotEmpty
              ? controller.selectedCategory
              : LocaleKeys.all.tr(),
        ),
      ),
      withBackground: true,
      actions: [
        IconButton(
          onPressed: () => controller.openFilters(),
          icon: SvgIcon(Assets.icons.filters.path),
        )
      ],
      child: Column(
        children: [
          Obx(
            () => controller.selectedCategory.isEmpty
                ? const SizedBox()
                : const Column(
                    children: [
                      Gap(24),
                      SubCategoriesList(),
                      Gap(8),
                      Divider(height: 0),
                    ],
                  ),
          ),
          Expanded(
            child: GridViewPagination.alignedCount(
              tag: ControllersTags.filters_results_pager,
              fromJson: MainActivity.fromJson,
              fetchApi: controller.fetchData,
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
              crossAxisCount: 1,
              mainAxisSpacing: 10,
              crossAxisSpacing: 10,
              initialLoading: const ListCardsLoading(),
              itemBuilder: (context, index, activity) =>
                  ActivityCard(activity: activity),
            ),
          ),
        ],
      ),
    );
  }
}
