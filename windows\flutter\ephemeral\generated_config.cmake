# Generated code do not commit.
file(TO_CMAKE_PATH "C:\\src\\versions\\3.29.3" FLUTTER_ROOT)
file(TO_CMAKE_PATH "D:\\work\\ixCoders\\current\\go_pal\\go_pal_flutter" PROJECT_DIR)

set(FLUTTER_VERSION "1.1.15+34" PARENT_SCOPE)
set(FLUTTER_VERSION_MAJOR 1 PARENT_SCOPE)
set(FLUTTER_VERSION_MINOR 1 PARENT_SCOPE)
set(FLUTTER_VERSION_PATCH 15 PARENT_SCOPE)
set(FLUTTER_VERSION_BUILD 34 PARENT_SCOPE)

# Environment variables to pass to tool_backend.sh
list(APPEND FLUTTER_TOOL_ENVIRONMENT
  "FLUTTER_ROOT=C:\\src\\versions\\3.29.3"
  "PROJECT_DIR=D:\\work\\ixCoders\\current\\go_pal\\go_pal_flutter"
  "FLUTTER_ROOT=C:\\src\\versions\\3.29.3"
  "FLUTTER_EPHEMERAL_DIR=D:\\work\\ixCoders\\current\\go_pal\\go_pal_flutter\\windows\\flutter\\ephemeral"
  "PROJECT_DIR=D:\\work\\ixCoders\\current\\go_pal\\go_pal_flutter"
  "FLUTTER_TARGET=D:\\work\\ixCoders\\current\\go_pal\\go_pal_flutter\\lib\\main.dart"
  "DART_OBFUSCATION=false"
  "TRACK_WIDGET_CREATION=true"
  "TREE_SHAKE_ICONS=false"
  "PACKAGE_CONFIG=D:\\work\\ixCoders\\current\\go_pal\\go_pal_flutter\\.dart_tool\\package_config.json"
)
