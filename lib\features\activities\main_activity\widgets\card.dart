import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart';
import 'package:gopal/core/localization/strings.dart';
import 'package:gopal/core/models/activity/constants/type.dart';
import 'package:gopal/core/routes.dart';
import 'package:gopal/core/style/assets/gen/assets.gen.dart';
import 'package:gopal/core/style/repo.dart';
import 'package:gopal/features/usecases/gender/widgets/gender_avatar.dart';
import 'package:gopal/features/widgets/general_componenets/buttons/elevated_button.dart';

import '../../usecases/open_sessions_calendar/index.dart';
import '../../usecases/specific_days_calendar/index.dart';
import '../controller.dart';
import '../../models/sub_activity_data.dart';
import 'card_body.dart';

class SubActivityCard extends GetView<MainActivityPageController> {
  final SubActivityData subActivity;
  const SubActivityCard({super.key, required this.subActivity});

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap:
          () => Nav.to(
            Pages.activity_details,
            arguments: subActivity.id,
            preventDuplicates: false,
          ),
      child: Container(
        decoration: BoxDecoration(
          color: StyleRepo.white,
          borderRadius: BorderRadius.circular(15),
          boxShadow: StyleRepo.elevation_3,
          border: Border.all(color: StyleRepo.grey.shade400, width: .5),
        ),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(15),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 12,
                ),
                decoration: BoxDecoration(
                  color: (subActivity.isAvailable
                          ? StyleRepo.turquoise
                          : StyleRepo.grey.shade600)
                      .withValues(alpha: .3),
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      (subActivity.isFamily ? tr(LocaleKeys.family) : "") +
                          (subActivity.isFamily && subActivity.season != null
                              ? " - "
                              : "") +
                          (subActivity.season != null
                              ? LocaleKeys.seasonal
                              : ""),
                      style: TextStyle(
                        color:
                            subActivity.isAvailable
                                ? StyleRepo.turquoise
                                : StyleRepo.grey.shade600,
                      ),
                    ),
                    Row(
                      children: [
                        Container(
                          height: 4,
                          width: 4,
                          decoration: const BoxDecoration(
                            color: StyleRepo.berkeleyBlue,
                            shape: BoxShape.circle,
                          ),
                        ),
                        const Gap(8),
                        Text(
                          tr(
                            subActivity.isAvailable
                                ? LocaleKeys.available
                                : LocaleKeys.unavailable,
                          ),
                          style: context.textTheme.titleMedium!.copyWith(
                            color: StyleRepo.berkeleyBlue,
                            fontWeight: FontWeight.w700,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
              const Gap(12),
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16),
                child: Text(
                  subActivity.name,
                  style: context.textTheme.titleMedium!.copyWith(
                    color: StyleRepo.blueViolet,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
              const Gap(12),
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16),
                child: Column(
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Row(
                          children: [
                            GenderAvatar(gender: subActivity.gender),
                            // const Gap(8),
                            // Text(tr(LocaleKeys.for_something,
                            //     args: [subActivity.gender.text]))
                          ],
                        ),
                        Row(
                          children: [
                            if (subActivity.priceBeforeDiscount != null)
                              Text(
                                "${subActivity.priceBeforeDiscount} ",
                                style: context.textTheme.bodyMedium!.copyWith(
                                  color: StyleRepo.grey.shade600,
                                  decoration: TextDecoration.lineThrough,
                                ),
                              ),
                            Text(
                              "${subActivity.price} ${tr(LocaleKeys.s_a_r)}",
                              style: context.textTheme.titleMedium!.copyWith(
                                color: StyleRepo.berkeleyBlue,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                    const Gap(12),
                    CardBody(activity: subActivity),
                    const Gap(12),
                    Row(
                      children: [
                        Expanded(
                          child: Container(
                            height: 35,
                            alignment: Alignment.center,
                            decoration: BoxDecoration(
                              color: StyleRepo.grey.shade200,
                              borderRadius: BorderRadius.circular(25),
                            ),
                            child: Text(
                              tr(LocaleKeys.details),
                              style: context.textTheme.titleMedium!.copyWith(
                                color: StyleRepo.grey.shade700,
                              ),
                            ),
                          ),
                        ),
                        if (subActivity.type != ActivityType.numbered_session)
                          const Gap(12),
                        if (subActivity.isAvailable &&
                            subActivity.type != ActivityType.numbered_session)
                          Expanded(
                            child: AppElevatedButton(
                              height: 35,
                              onPressed: () {
                                if (subActivity.type ==
                                    ActivityType.open_sessions) {
                                  return Get.dialog(
                                    _OpenSessionsDialog(
                                      subActivity: subActivity,
                                    ),
                                  );
                                }
                                if (subActivity.type ==
                                    ActivityType.specific_days) {
                                  return Get.dialog(
                                    _SpecificDaysDialog(
                                      subActivity: subActivity,
                                    ),
                                  );
                                }
                                if (subActivity.type ==
                                    ActivityType.periodic_days) {
                                  return controller.bookAPeriodic(
                                    activityId: subActivity.id,
                                    periodId: subActivity.periodic!.id,
                                  );
                                }
                                if (subActivity.type == ActivityType.ticket) {
                                  return controller.bookATicket(
                                    activityId: subActivity.id,
                                    minPerson: subActivity.minPersonsNumber!,
                                    maxPerson: subActivity.maxPersonsNumber!,
                                  );
                                }
                                if (subActivity.type ==
                                    ActivityType.custom_session) {
                                  return controller.bookCustomSession(
                                    activityId: subActivity.id,
                                  );
                                }
                              },
                              child: Text(tr(LocaleKeys.booking)),
                            ),
                          ),
                      ],
                    ),
                    const Gap(12),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class _SpecificDaysDialog extends StatelessWidget {
  const _SpecificDaysDialog({required this.subActivity});

  final SubActivityData subActivity;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 24, vertical: Get.height * .05),
      child: Material(
        color: Colors.transparent,
        child: Stack(
          alignment: Alignment.topCenter,
          children: [
            Container(
              margin: const EdgeInsets.only(top: 40),
              decoration: BoxDecoration(
                color: StyleRepo.white,
                borderRadius: BorderRadius.circular(20),
              ),
              child: SingleChildScrollView(
                padding: const EdgeInsets.only(top: 40, bottom: 12),
                child: SpecificDaysDataWidget(
                  id: subActivity.id,
                  specificDayId: subActivity.specificDayId!,
                  minDate: subActivity.minDate,
                  maxDate: subActivity.maxDate,
                ),
              ),
            ),
            Assets.icons.calendarSticker.svg(),
          ],
        ),
      ),
    );
  }
}

class _OpenSessionsDialog extends StatelessWidget {
  const _OpenSessionsDialog({required this.subActivity});

  final SubActivityData subActivity;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 24, vertical: Get.height * .05),
      child: Material(
        color: Colors.transparent,
        child: Stack(
          alignment: Alignment.topCenter,
          children: [
            Container(
              margin: const EdgeInsets.only(top: 40),
              decoration: BoxDecoration(
                color: StyleRepo.white,
                borderRadius: BorderRadius.circular(20),
              ),
              child: SingleChildScrollView(
                padding: const EdgeInsets.only(top: 40, bottom: 12),
                child: OpenSessionsDataWidget(
                  id: subActivity.id,
                  priceType: subActivity.priceType,
                  minDate: subActivity.minDate!,
                  maxDate: subActivity.maxDate!,
                  minPersons: subActivity.minPersonsNumber,
                  maxPersons: subActivity.maxPersonsNumber,
                ),
              ),
            ),
            Assets.icons.calendarSticker.svg(),
          ],
        ),
      ),
    );
  }
}
