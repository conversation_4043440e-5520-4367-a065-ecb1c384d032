import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:gopal/core/services/notifications_counter/counter.dart';
import 'package:gopal/features/main/models/destinations.dart';
import 'package:gopal/features/widgets/general_componenets/svg_icon.dart';

import '../controller.dart';

class MainNavBar extends StatelessWidget {
  const MainNavBar({super.key});

  @override
  Widget build(BuildContext context) {
    MainPageController controller = Get.find();

    return Obx(
      () => NavigationBar(
        onDestinationSelected:
            (destination) =>
                controller.currentDestination =
                    MainDestinations.values[destination],
        selectedIndex: MainDestinations.values.indexOf(
          controller.currentDestination,
        ),
        destinations:
            MainDestinations.values.map((destination) {
              return NavigationDestination(
                icon:
                    destination == MainDestinations.notifications
                        ? Obx(
                          () => Badge(
                            isLabelVisible:
                                NotificationsCounter.notificationsCount > 0,
                            label: Text(
                              "${NotificationsCounter.notificationsCount >= 10 ? "+9" : NotificationsCounter.notificationsCount}",
                            ),
                            child: SvgIcon(destination.iconPath),
                          ),
                        )
                        : SvgIcon(destination.iconPath),
                label: destination.text,
              );
            }).toList(),
      ),
    );
  }
}
