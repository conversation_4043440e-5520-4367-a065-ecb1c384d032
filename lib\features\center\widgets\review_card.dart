import 'package:flutter/material.dart';
import 'package:flutter_rating_bar/flutter_rating_bar.dart';
import 'package:gap/gap.dart';
import 'package:gopal/core/style/repo.dart';
import 'package:gopal/core/widgets/image.dart';
import 'package:gopal/features/widgets/general_componenets/svg_icon.dart';

import '../models/center_review.dart';

class ReviewCard extends StatelessWidget {
  final CenterReview review;
  const ReviewCard({super.key, required this.review});

  @override
  Widget build(BuildContext context) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        AppImage(
          path: review.user.image?.small ?? "",
          type: ImageType.CachedNetwork,
          height: 50,
          width: 50,
          decoration: const BoxDecoration(shape: BoxShape.circle),
        ),
        const Gap(12),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(review.user.name),
              RatingBar.builder(
                itemSize: 17,
                unratedColor: Colors.transparent,
                allowHalfRating: true,
                ignoreGestures: true,
                initialRating: review.rate,
                itemCount: 5,
                itemBuilder: (_, __) {
                  return SvgIcon(
                    Assets.icons.star.path,
                    color: StyleRepo.yellow.shade600,
                  );
                },
                onRatingUpdate: (_) {},
              ),
              Text(review.comment ?? ""),
            ],
          ),
        ),
      ],
    );
  }
}
