import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart' hide Trans;
import 'package:gopal/core/localization/strings.dart';
import 'package:gopal/core/services/state_management/widgets/obs_widget.dart';
import 'package:gopal/core/widgets/loading.dart';
import 'package:gopal/features/widgets/general_componenets/fields/field_error.dart';
import 'package:multi_dropdown/multiselect_dropdown.dart';

import '../controller.dart';
import '../models/categories.dart/main_category.dart';

class SubcategoriesFilter extends GetView<FiltersPageController> {
  const SubcategoriesFilter({super.key});

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Gap(24),
        Text(
          LocaleKeys.subcategories.tr(),
          style: context.textTheme.titleMedium!.copyWith(
            fontWeight: FontWeight.w700,
          ),
        ),
        const Gap(6),
        ObsListBuilder(
          obs: controller.subcategories,
          loader: (_) => const FieldLoadingWidget(),
          errorBuilder:
              (_, error) => FieldErrorWidget(
                error: controller.subcategories.error!,
                onRefresh:
                    () => controller.loadSubcategories(
                      controller.selectedCategory.id,
                    ),
              ),
          builder: (context, subcategories) {
            return ChipTheme(
              data: const ChipThemeData(),
              child: MultiSelectDropDown<CategoryFilter>(
                controller: controller.subcategoriesController,
                options: const [],
                onOptionSelected: (selections) {
                  controller.calculatePricentage();
                  controller.subcategoriesStream.add(
                    controller.subcategoriesController.selectedOptions
                        .map((e) => e.value!)
                        .toList(),
                  );
                },
                chipConfig: const ChipConfig(
                  padding: EdgeInsets.symmetric(horizontal: 12),
                ),
                hint: LocaleKeys.none.tr(),
                inputDecoration: BoxDecoration(
                  border: Border.fromBorderSide(
                    Theme.of(
                      context,
                    ).inputDecorationTheme.enabledBorder!.borderSide,
                  ),
                  borderRadius:
                      (Theme.of(context).inputDecorationTheme.enabledBorder!
                              as OutlineInputBorder)
                          .borderRadius,
                ),
                suffixIcon: const Icon(Icons.keyboard_arrow_down),
              ),
            );
          },
        ),
      ],
    );
  }
}
