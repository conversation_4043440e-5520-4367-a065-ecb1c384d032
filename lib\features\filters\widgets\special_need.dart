import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart' hide Trans;
import 'package:gopal/core/localization/strings.dart';
import 'package:gopal/core/models/selection.dart';
import 'package:gopal/core/services/state_management/widgets/obs_widget.dart';
import 'package:gopal/core/widgets/loading.dart';
import 'package:gopal/features/widgets/general_componenets/dropdown.dart';
import 'package:gopal/features/widgets/general_componenets/fields/field_error.dart';

import '../controller.dart';

class SpecialNeedFilter extends GetView<FiltersPageController> {
  const SpecialNeedFilter({super.key});

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Gap(24),
        Text(
          LocaleKeys.special_need.tr(),
          style: context.textTheme.titleMedium!.copyWith(
            fontWeight: FontWeight.w700,
          ),
        ),
        const Gap(6),
        ObsListBuilder(
          obs: controller.specialCases,
          loader: (_) => const FieldLoadingWidget(),
          errorBuilder:
              (context, error) => FieldErrorWidget(
                error: error,
                onRefresh: () => controller.refreshSpecialCases(),
              ),
          builder: (context, needs) {
            return Obx(() {
              controller.specialNeed.status;
              return AppDropdown<Selection?>(
                value: controller.specialNeed.value,
                items: List.generate(
                  needs.length + 1,
                  (index) =>
                      index == 0
                          ? DropdownMenuItem<Selection?>(
                            value: null,
                            child: Text(LocaleKeys.none.tr()),
                          )
                          : DropdownMenuItem<Selection?>(
                            value: needs[index - 1],
                            child: Text(needs[index - 1].name),
                          ),
                ),
                onChanged: (value) => controller.specialNeed.value = value,
              );
            });
          },
        ),
      ],
    );
  }
}
