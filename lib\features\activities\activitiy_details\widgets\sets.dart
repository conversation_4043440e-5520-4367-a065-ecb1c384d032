import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart' hide Trans;
import 'package:gopal/core/localization/strings.dart';
import 'package:gopal/core/services/state_management/widgets/obs_widget.dart';
import 'package:gopal/core/style/repo.dart';
import 'package:gopal/core/widgets/loading.dart';
import 'package:gopal/features/widgets/general_componenets/buttons/elevated_button.dart';
import 'package:gopal/features/widgets/general_componenets/fields/field_error.dart';

import '../../widgets/sets.dart';
import '../controller.dart';

class ActivitySets extends StatelessWidget {
  final int id;
  const ActivitySets({super.key, required this.id});

  @override
  Widget build(BuildContext context) {
    ActivityDetailsPageController controller = Get.find(tag: "$id");
    return ObsListBuilder(
      obs: controller.sets,
      loader:
          (_) => Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  LocaleKeys.available_sets.tr(),
                  style: context.textTheme.bodyLarge!.copyWith(
                    color: StyleRepo.blueViolet,
                    fontWeight: FontWeight.w600,
                    decoration: TextDecoration.underline,
                  ),
                ),
                const Gap(12),
                ListView.separated(
                  shrinkWrap: true,
                  physics: const NeverScrollableScrollPhysics(),
                  itemCount: 3,
                  separatorBuilder: (_, __) => const Gap(12),
                  itemBuilder: (_, __) => const FieldLoadingWidget(),
                ),
              ],
            ),
          ),
      errorBuilder:
          (context, error) => Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: Column(
              children: [
                Text(
                  LocaleKeys.available_sets.tr(),
                  style: context.textTheme.bodyLarge!.copyWith(
                    color: StyleRepo.blueViolet,
                    fontWeight: FontWeight.w600,
                    decoration: TextDecoration.underline,
                  ),
                ),
                const Gap(12),
                FieldErrorWidget(
                  error: error,
                  onRefresh: () => controller.refreshSets(),
                ),
              ],
            ),
          ),
      builder: (context, sets) {
        if (sets.length == 1 && sets.first.sessions.length == 1) {
          return Padding(
            padding: EdgeInsets.symmetric(horizontal: Get.width * .1),
            child: AppElevatedButton(
              onPressed:
                  controller.activity.value!.isAvailable
                      ? () => controller.bookASet(
                        activityId: controller.activity.value!.id,
                        setId: sets.first.id,
                      )
                      : null,
              child: Text(LocaleKeys.booking.tr()),
            ),
          );
        }
        return Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: SetsWidget(
            sets: sets,
            onBook:
                !controller.activity.value!.isAvailable
                    ? null
                    : (setId) => controller.bookASet(
                      activityId: controller.activity.value!.id,
                      setId: setId,
                    ),
          ),
        );
      },
    );
  }
}
