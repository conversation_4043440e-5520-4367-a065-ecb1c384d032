import 'package:flutter/material.dart';
import 'package:flutter_rating_bar/flutter_rating_bar.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart' hide Trans;
import 'package:gopal/core/style/repo.dart';
import 'package:gopal/features/widgets/general_componenets/svg_icon.dart';
import 'package:gopal/features/widgets/provider_image.dart';

import '../controller.dart';
import '../models/center_details.dart';

class CenterProfileHeader extends StatelessWidget {
  final CenterDetails center;
  const CenterProfileHeader({super.key, required this.center});

  @override
  Widget build(BuildContext context) {
    CenterProfilePageController controller = Get.find(tag: "${center.id}");
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Row(
        children: [
          CenterImage(
            image: center.profileImage.small,
            isTrusted: center.isTrusted,
            radius: 60,
          ),
          const Gap(12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(center.name, style: context.textTheme.titleMedium),
                Row(
                  children: [
                    RatingBar.builder(
                      itemSize: 20,
                      unratedColor: Colors.transparent,
                      allowHalfRating: true,
                      ignoreGestures: true,
                      initialRating: center.rate,
                      itemCount: center.rate.ceil(),
                      itemBuilder: (_, __) {
                        return SvgIcon(
                          Assets.icons.star.path,
                          color: StyleRepo.yellow.shade600,
                        );
                      },
                      onRatingUpdate: (_) {},
                    ),
                  ],
                ),
              ],
            ),
          ),
          const Gap(12),
          if (center.roomId != null)
            IconButton(
              onPressed: () => controller.navToChat(center.toChatRoom()!),
              style: const ButtonStyle(
                side: WidgetStatePropertyAll(BorderSide(color: StyleRepo.grey)),
              ),
              icon: Icon(
                Icons.add_comment_outlined,
                color: StyleRepo.grey.shade700,
              ),
            ),
        ],
      ),
    );
  }
}
