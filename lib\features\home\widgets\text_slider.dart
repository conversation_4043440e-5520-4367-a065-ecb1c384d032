import 'package:carousel_slider/carousel_slider.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:gopal/core/services/state_management/widgets/obs_widget.dart';
import 'package:gopal/core/style/repo.dart';
import 'package:gopal/features/home/<USER>/ad.dart';

import '../controller.dart';

class HomeTextSlider extends StatelessWidget {
  const HomeTextSlider({super.key});

  @override
  Widget build(BuildContext context) {
    HomePageController controller = Get.find();
    return ObsListBuilder(
      obs: controller.ads,
      loader: (context) => const SizedBox(),
      errorBuilder: (_, __) => const SizedBox(),
      builder: (context, data) {
        List ads = List.from(
          data.where((element) => element.type == AdType.text),
        );
        if (ads.isEmpty) {
          return const SizedBox();
        }
        return CarouselSlider.builder(
          itemCount: ads.length,
          options: CarouselOptions(
            autoPlay: true,
            autoPlayAnimationDuration: 3.seconds,
            autoPlayInterval: 7.seconds,
            height: 70,
            viewportFraction: 1,
          ),
          itemBuilder: (context, index, _) {
            return InkWell(
              onTap: () => controller.adClick(ads[index]),
              child: Container(
                width: Get.width,
                height: 50,
                padding: const EdgeInsets.symmetric(horizontal: 16),
                color: StyleRepo.turquoise.withValues(alpha: .35),
                alignment: Alignment.center,
                child: Text(
                  ads[index].text,
                  textAlign: TextAlign.center,
                  style: context.textTheme.bodyMedium!.copyWith(height: 1),
                ),
              ),
            );
          },
        );
      },
    );
  }
}
