import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:gopal/core/constants/controllers_tags.dart';
import 'package:gopal/core/models/activity/main_activity.dart';
import 'package:gopal/core/models/user/center.dart';
import 'package:gopal/core/services/pagination/controller.dart';
import 'package:gopal/core/services/rest_api/rest_api.dart';
import 'package:gopal/core/utils/action_stack.dart';

class SearchPageController extends GetxController
    with GetSingleTickerProviderStateMixin {
  late TabController tabController;

  late TextEditingController centersSearch, activitiesSearch;

  @override
  void onInit() {
    centersSearch = TextEditingController();
    activitiesSearch = TextEditingController();
    tabController = TabController(length: 2, vsync: this);
    debouncerActivitiesSearch = ActionStack(execute: onActivitySearchChanged);
    debouncerCentersSearch = ActionStack(execute: onCentersSearchChanged);
    super.onInit();
  }

  late ActionStack<String> debouncerActivitiesSearch;
  onActivitySearchChanged(String s) {
    isSearchingForActivities = s.trim().isNotEmpty;
    if (isSearchingForActivities) {
      activitiesPager?.refreshData();
    } else {
      Get.delete<PaginationController<MainActivity>>(
        tag: ControllersTags.activities_search_pager,
      );
    }
  }

  late ActionStack<String> debouncerCentersSearch;
  onCentersSearchChanged(String s) {
    isSearchingForCenters = s.trim().isNotEmpty;
    if (isSearchingForCenters) {
      centersPager?.refreshData();
    } else {
      Get.delete<PaginationController<CenterModel>>(
        tag: ControllersTags.centers_search_pager,
      );
    }
  }

  final Rx<bool> _isSearchingForCenters = false.obs;
  bool get isSearchingForCenters => _isSearchingForCenters.value;
  set isSearchingForCenters(bool value) => _isSearchingForCenters.value = value;

  final Rx<bool> _isSearchingForActivities = false.obs;
  bool get isSearchingForActivities => _isSearchingForActivities.value;
  set isSearchingForActivities(bool value) =>
      _isSearchingForActivities.value = value;

  Future<ResponseModel> fetchCenters(int page, CancelToken cancel) async {
    return await APIService.instance.requestAPI(
      Request(
        endPoint: EndPoints.centers,
        params: {"search_key": centersSearch.text.trim(), "page": page},
        cancelToken: cancel,
      ),
    );
  }

  Future<ResponseModel> fetchActivities(int page, CancelToken cancel) async {
    return await APIService.instance.requestAPI(
      Request(
        endPoint: EndPoints.main_activities,
        params: {"search_key": activitiesSearch.text.trim(), "page": page},
        cancelToken: cancel,
      ),
    );
  }

  PaginationController<CenterModel>? get centersPager {
    if (Get.isRegistered<PaginationController<CenterModel>>(
      tag: ControllersTags.centers_search_pager,
    )) {
      return Get.find<PaginationController<CenterModel>>(
        tag: ControllersTags.centers_search_pager,
      );
    }
    return null;
  }

  PaginationController<MainActivity>? get activitiesPager {
    if (Get.isRegistered<PaginationController<MainActivity>>(
      tag: ControllersTags.activities_search_pager,
    )) {
      return Get.find<PaginationController<MainActivity>>(
        tag: ControllersTags.activities_search_pager,
      );
    }
    return null;
  }

  @override
  void onClose() {
    centersSearch.dispose();
    activitiesSearch.dispose();
    super.onClose();
  }
}
