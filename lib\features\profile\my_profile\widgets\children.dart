import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart' hide Trans;
import 'package:gopal/core/localization/strings.dart';
import 'package:gopal/core/style/repo.dart';
import 'package:gopal/features/usecases/confirmation/index.dart';
import 'package:gopal/features/widgets/general_componenets/buttons/outlined_button.dart';
import 'package:gopal/features/widgets/general_componenets/svg_icon.dart';

import '../controller.dart';

class ProfileChildren extends GetView<ProfilePageController> {
  const ProfileChildren({super.key});

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Align(
          alignment: AlignmentDirectional.centerStart,
          child: Text(
            LocaleKeys.children.tr(),
            style: context.textTheme.headlineSmall!.copyWith(
              color: StyleRepo.blueViolet,
              fontWeight: FontWeight.w600,
            ),
          ),
        ),
        Gap(12),
        Obx(
          () => ListView.separated(
            itemCount: controller.children.length,
            separatorBuilder: (_, __) => const Gap(16),
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            padding: EdgeInsets.zero,
            itemBuilder:
                (context, index) => Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 24),
                  child: AppOutlinedButton(
                    onTap: () => controller.openChildProfile(index),
                    child: Row(
                      children: [
                        const Padding(
                          padding: EdgeInsets.all(8.0),
                          child: Icon(Icons.arrow_back_ios),
                        ),
                        Expanded(
                          child: Text(
                            controller.children[index].firstName,
                            textAlign: TextAlign.center,
                          ),
                        ),
                        IconButton(
                          onPressed:
                              () => ConfirmationDialog.confirm(
                                onAccept: () => controller.deleteChild(index),
                                body: LocaleKeys
                                    .delete_child_confirmation_message
                                    .tr(
                                      args: [
                                        controller.children[index].firstName,
                                      ],
                                    ),
                              ),
                          icon: Container(
                            padding: const EdgeInsets.all(8),
                            decoration: BoxDecoration(
                              color: StyleRepo.red.shade100,
                              shape: BoxShape.circle,
                              border: Border.all(
                                color: StyleRepo.red,
                                width: .5,
                              ),
                            ),
                            child: SvgIcon(
                              Assets.icons.delete.path,
                              color: StyleRepo.red,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
          ),
        ),
        const Gap(16),
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 24),
          child: AppOutlinedButton(
            onTap: () => controller.addChild(),
            backgroundColor: StyleRepo.white,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Container(
                  padding: const EdgeInsets.all(4),
                  decoration: BoxDecoration(
                    color: StyleRepo.turquoise.withValues(alpha: .3),
                    shape: BoxShape.circle,
                  ),
                  child: const Icon(Icons.add, size: 20),
                ),
                const Gap(10),
                Text(LocaleKeys.add_child.tr()),
              ],
            ),
          ),
        ),
      ],
    );
  }
}
