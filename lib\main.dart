import 'dart:async';
import 'dart:developer';
import 'dart:io';

import 'package:easy_localization/easy_localization.dart';

import 'package:flutter/material.dart';
import 'package:flutter_image_compress/flutter_image_compress.dart';
import 'package:get/get.dart';
import 'package:gopal/core/style/style.dart';
import 'package:image_picker/image_picker.dart';
import 'package:path_provider/path_provider.dart';
import 'core/config/app_builder.dart';
import 'core/config/defaults.dart';
import 'core/localization/localization.dart';
import 'core/routes.dart';
import 'core/utils/file_utils.dart';
import 'features/splash_screen/index.dart';

class MyHttpOverrides extends HttpOverrides {
  @override
  HttpClient createHttpClient(SecurityContext? context) {
    return super.createHttpClient(context)
      ..badCertificateCallback =
          (X509Certificate cert, String host, int port) => true;
  }
}

void main() async {
  log("بسم الله الرحمن الرحيم");

  WidgetsFlutterBinding.ensureInitialized();

  HttpOverrides.global = MyHttpOverrides();

  await EasyLocalization.ensureInitialized();

  runApp(
    EasyLocalization(
      supportedLocales: AppLocalization.values.map((e) => e.locale).toList(),
      path: "assets/translations",
      fallbackLocale: AppLocalization.en.locale,
      child: const MyApp(),
    ),
  );
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    Default.preferredOrientation();
    Get.put(AppBuilder());

    return GetMaterialApp(
      debugShowCheckedModeBanner: false,
      title: Default.appTitle,
      theme: AppStyle.lightTheme(context.locale.languageCode == "ar"),
      themeMode: ThemeMode.light,
      localizationsDelegates: context.localizationDelegates,
      locale: context.locale,
      supportedLocales: context.supportedLocales,
      initialRoute: '/',
      unknownRoute: AppRouting.unknownRoute,
      getPages: AppRouting.routes(),
      home: const SplashScreen(),
    );
  }
}

class TestCompressingImage extends StatefulWidget {
  const TestCompressingImage({super.key});

  @override
  State<TestCompressingImage> createState() => _TestCompressingImageState();
}

class _TestCompressingImageState extends State<TestCompressingImage> {
  String image = "";
  String imageSize = "";

  pickImage() {
    setState(() async {
      image = (await pickAndCompressImage()) ?? "";
    });
  }

  Future<String?> pickAndCompressImage() async {
    XFile? pickedImage = await ImagePicker().pickImage(
      source: ImageSource.gallery,
    );
    if (pickedImage == null) {
      return null;
    }

    String? result = await compressAndGetFile(
      File(pickedImage.path),
      "${(await getTemporaryDirectory()).path}/${DateTime.now()}.jpg",
    );

    return result ?? pickedImage.path;
  }

  Future<String?> compressAndGetFile(File file, String targetPath) async {
    log(FileUtils.fileSize(file.lengthSync()), name: "Image Compress");
    log(targetPath, name: "Image Compress");
    XFile? result = await FlutterImageCompress.compressAndGetFile(
      file.absolute.path,
      targetPath,
      quality: 100,
      minWidth: 500,
      minHeight: 500,
      autoCorrectionAngle: false,
    );
    imageSize = FileUtils.fileSize((await result?.length())!);
    log(FileUtils.fileSize((await result?.length())!), name: "Image Compress");
    return result?.path;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      floatingActionButton: FloatingActionButton(onPressed: pickImage),
      body: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Center(
            child:
                image.isNotEmpty
                    ? Image.file(File(image), width: 300, height: 500)
                    : const SizedBox(),
          ),
          Text(imageSize, style: const TextStyle(fontSize: 26)),
          ElevatedButton(
            onPressed: () async {
              // TODO - save image
              // File file = File(image);
              // await ImageGallerySaver.saveImage(await file.readAsBytes());
            },
            child: const Text("save"),
          ),
        ],
      ),
    );
  }
}
