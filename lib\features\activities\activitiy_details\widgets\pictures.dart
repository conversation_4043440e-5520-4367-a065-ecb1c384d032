import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart' hide Trans;
import 'package:gopal/core/localization/strings.dart';
import 'package:gopal/core/models/image.dart';
import 'package:gopal/core/style/repo.dart';
import 'package:gopal/core/widgets/image.dart';
import 'package:gopal/features/usecases/pictures/index.dart';
import 'package:gopal/features/widgets/general_componenets/svg_icon.dart';

class ActivityPictures extends StatelessWidget {
  final List<ImageModel> media;
  const ActivityPictures({super.key, required this.media});

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: Text(
            LocaleKeys.pictures_related_to_the_activity.tr(),
            style: context.textTheme.bodyLarge!.copyWith(
              color: StyleRepo.blueViolet,
              fontWeight: FontWeight.w600,
              decoration: TextDecoration.underline,
            ),
          ),
        ),
        const Gap(12),
        SizedBox(
          height: 150,
          child: ListView.separated(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            scrollDirection: Axis.horizontal,
            itemCount: media.length,
            separatorBuilder: (_, __) => const Gap(10),
            itemBuilder: (context, index) {
              return GestureDetector(
                onTap:
                    () => MediaPreview.preview(
                      media: media.map((e) => e.original).toList(),
                      initialIndex: index,
                    ),
                child: AspectRatio(
                  aspectRatio: 1,
                  child: Stack(
                    children: [
                      AppImage(
                        path: media[index].medium,
                        type: ImageType.CachedNetwork,
                        height: double.infinity,
                        width: double.infinity,
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                      if (media[index].isVideo)
                        Container(
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(8),
                            color: Colors.black38,
                          ),
                          child: Center(
                            child: Container(
                              padding: const EdgeInsets.all(16),
                              decoration: BoxDecoration(
                                shape: BoxShape.circle,
                                border: Border.all(color: StyleRepo.white),
                              ),
                              child: SvgIcon(
                                Assets.icons.play.path,
                                color: StyleRepo.white,
                              ),
                            ),
                          ),
                        ),
                    ],
                  ),
                ),
              );
            },
          ),
        ),
      ],
    );
  }
}
