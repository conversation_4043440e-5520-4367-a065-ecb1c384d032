class Selection {
  int id;
  String name;

  Selection({required this.id, required this.name});

  factory Selection.fromJson(Map<String, dynamic> json) {
    return Selection(id: json['id'], name: json['name']);
  }

  static Selection all(String name) => Selection(id: 0, name: name);

  @override
  int get hashCode => id;

  @override
  bool operator ==(Object other) {
    return other is Selection ? id == other.id : false;
  }
}
