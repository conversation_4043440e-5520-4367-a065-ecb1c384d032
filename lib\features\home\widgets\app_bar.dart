import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:gopal/core/localization/strings.dart';
import 'package:gopal/core/routes.dart';
import 'package:gopal/core/style/repo.dart';
import 'package:gopal/features/activities/cart/widgets/cart_icon.dart';
import 'package:gopal/features/usecases/cities_filter/cities_dropdown.dart';
import 'package:gopal/features/widgets/general_componenets/app_bars/general_app_bar.dart';
import 'package:gopal/features/widgets/general_componenets/svg_icon.dart';
import 'package:hotspot/hotspot.dart';

import '../controller.dart';

class HomeAppBar extends GetView<HomePageController> {
  const HomeAppBar({super.key});

  @override
  Widget build(BuildContext context) {
    return GeneralAppBar(
      title: SizedBox(
        width: Get.width * .35,
        child: CitiesFilter(),
      ).withHotspot(
        order: 1,
        title: tr(LocaleKeys.cities_filter),
        text: tr(LocaleKeys.cities_filter_description),
      ),
      centerTitle: false,
      actions: [
        CartIcon().withHotspot(
          order: 2,
          title: tr(LocaleKeys.the_cart),
          text: tr(LocaleKeys.cart_description),
        ),
        IconButton(
          onPressed: () => Nav.to(Pages.search),
          icon: const Icon(Icons.search, size: 30),
        ),
        IconButton(
          onPressed: () => Scaffold.of(context).openEndDrawer(),
          icon: SvgIcon(Assets.icons.sideBar.path, color: StyleRepo.white),
        ).withHotspot(
          order: 3,
          title: tr(LocaleKeys.general_settings),
          text: tr(LocaleKeys.general_settings_description),
        ),
      ],
    );
  }
}
