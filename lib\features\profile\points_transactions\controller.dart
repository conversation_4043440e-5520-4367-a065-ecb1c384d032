import 'package:dio/dio.dart';
import 'package:get/get.dart';
import 'package:gopal/core/services/rest_api/rest_api.dart';

class PointsTransactionsPageController extends GetxController {
  Future<ResponseModel> fetchPointsTransactions(
    int page,
    CancelToken cancel,
  ) async {
    final response = await APIService.instance.requestAPI(
      Request(
        endPoint: EndPoints.points_transactions,
        params: {"page": page},
        cancelToken: cancel,
      ),
    );
    response.data = response.data['transactions']['data'];
    return response;
  }
}
