import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart';
import 'package:gopal/core/config/app_builder.dart';
import 'package:gopal/core/localization/strings.dart';
import 'package:gopal/core/style/repo.dart';
import 'package:gopal/features/widgets/general_componenets/app_bars/general_app_bar.dart';
import 'package:gopal/features/widgets/general_componenets/buttons/elevated_button.dart';
import 'package:gopal/features/widgets/general_componenets/svg_icon.dart';

import 'controller.dart';

class InvitationPage extends StatelessWidget {
  const InvitationPage({super.key});

  @override
  Widget build(BuildContext context) {
    // ignore: unused_local_variable
    InvitationPageController controller = Get.put(InvitationPageController());
    return Scaffold(
      appBar: GeneralAppBar(title: Text(tr(LocaleKeys.invite_friends))),
      body: ListView(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        children: [
          Assets.images.inviteFriends.image(),
          const Gap(12),
          Text(
            tr(LocaleKeys.your_invitation_code),
            textAlign: TextAlign.center,
            style: context.textTheme.titleLarge!.copyWith(
              color: StyleRepo.turquoise,
              fontWeight: FontWeight.bold,
            ),
          ),
          const Gap(16),
          Text(
            Get.find<AppBuilder>().user.value!.code,
            textAlign: TextAlign.center,
            style: context.textTheme.headlineMedium!.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const Gap(12),
          TextButton(
            onPressed: controller.copyCode,
            child: Text(tr(LocaleKeys.copy_the_code)),
          ),
          const Gap(12),
          Text(tr(LocaleKeys.invitation_message), textAlign: TextAlign.center),
          const Gap(12),
          AppElevatedButton(
            onPressed: controller.share,
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                SvgIcon(Assets.icons.share.path),
                const Gap(12),
                Text(tr(LocaleKeys.share)),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
