import 'package:dio/dio.dart';
import 'package:get/get.dart';
import 'package:gopal/core/constants/controllers_tags.dart';
import 'package:gopal/core/models/book/book.dart';
import 'package:gopal/core/models/book/status.dart';
import 'package:gopal/core/services/pagination/controller.dart';
import 'package:gopal/core/services/rest_api/rest_api.dart';
import 'package:gopal/core/widgets/loading.dart';
import 'package:gopal/features/usecases/toast/toast.dart';

class CartPageController extends GetxController {
  Future<ResponseModel> fetchPending(int page, CancelToken cancel) async {
    Map<String, dynamic> params = {
      "page": page,
      "statuses[]": BookStatus.pending.value,
    };
    return await APIService.instance.requestAPI(
      Request(
        endPoint: EndPoints.user_books,
        params: params,
        cancelToken: cancel,
      ),
    );
  }

  //SECTION - Delete Book
  deleteBook(BookedActivity bookedActivity) async {
    Loading.show();
    ResponseModel response = await APIService.instance.requestAPI(
      Request(
        endPoint: EndPoints.delete_book(bookedActivity.id),
        method: RequestMethod.Delete,
      ),
    );
    Loading.dispose();
    if (response.success) {
      try {
        Get.find<PaginationController<BookedActivity>>(
          tag: ControllersTags.my_pending_activities,
        ).data.remove(bookedActivity);
      } catch (_) {}
    } else {
      Toast.show(message: response.message);
    }
  }

  //!SECTION
}
