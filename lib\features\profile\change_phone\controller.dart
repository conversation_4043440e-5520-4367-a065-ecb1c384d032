import 'package:country_picker/country_picker.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:gopal/core/routes.dart';
import 'package:gopal/core/services/rest_api/rest_api.dart';
import 'package:gopal/core/widgets/loading.dart';
import 'package:gopal/features/auth/verification/models/nav.dart';
import 'package:gopal/features/usecases/toast/toast.dart';

class ChangePhonePageController extends GetxController {
  Country selectedCountry = Country.parse("SA");

  final formKey = GlobalKey<FormState>();

  TextEditingController phone = TextEditingController();

  confirm() async {
    if (!formKey.currentState!.validate()) return;

    String phoneNumber =
        "+${selectedCountry.phoneCode}${phone.text.replaceFirst(RegExp(r'^0+'), '')}";

    Loading.show();

    ResponseModel response = await APIService.instance.requestAPI(
      Request(
        endPoint: EndPoints.change_phone,
        method: RequestMethod.Post,
        body: {"phone": phoneNumber},
      ),
    );

    Loading.dispose();
    if (response.success) {
      Nav.to(
        Pages.verification,
        arguments: VerificationPageNav(
          phone: phoneNumber,
          isChangePhoneVerification: true,
        ),
      );
    } else {
      Toast.show(message: response.message);
    }
  }
}
