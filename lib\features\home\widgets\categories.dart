import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart';
import 'package:gopal/core/routes.dart';
import 'package:gopal/core/services/state_management/widgets/obs_widget.dart';
import 'package:gopal/core/style/repo.dart';
import 'package:gopal/core/widgets/shimmer_loading.dart';
import 'package:gopal/features/categories/category/models/nav.dart';
import 'package:gopal/features/widgets/general_componenets/fields/field_error.dart';

import '../controller.dart';

class CategoriesListWidget extends GetView<HomePageController> {
  const CategoriesListWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return ObsListBuilder(
      obs: controller.categories,
      loader:
          (context) => SizedBox(
            height: 95,
            child: ListView.separated(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              scrollDirection: Axis.horizontal,
              itemCount: 10,
              separatorBuilder: (_, __) => const Gap(12),
              itemBuilder: (context, index) {
                return Column(
                  children: [
                    ShimmerWidget.card(
                      height: 70,
                      width: 70,
                      borderRadius: BorderRadius.circular(35),
                    ),
                    const Gap(8),
                    const ShimmerWidget.card(height: 17, width: 60),
                  ],
                );
              },
            ),
          ),
      errorBuilder:
          (context, error) => FieldErrorWidget(
            error: error,
            onRefresh: () => controller.refreshWhenError(),
          ),
      builder: (context, categories) {
        return SizedBox(
          height: 100,
          child: ListView.separated(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            scrollDirection: Axis.horizontal,
            itemCount: categories.length,
            separatorBuilder: (_, __) => const Gap(12),
            itemBuilder: (context, index) {
              return InkWell(
                onTap:
                    () => Nav.to(
                      Pages.category,
                      arguments: CategoryPageNav(categories[index]),
                    ),
                child: Column(
                  children: [
                    Container(
                      height: 70,
                      width: 70,
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        border: Border.all(color: StyleRepo.grey),
                      ),
                      child: AspectRatio(
                        aspectRatio: 1,
                        child:
                            categories[index].svg != null &&
                                    categories[index].svg!.isNotEmpty
                                ? SvgPicture.network(categories[index].svg!)
                                : const SizedBox(),
                      ),
                    ),
                    const Gap(4),
                    Text(
                      categories[index].name,
                      style: const TextStyle(fontWeight: FontWeight.bold),
                    ),
                  ],
                ),
              );
            },
          ),
        );
      },
    );
  }
}
