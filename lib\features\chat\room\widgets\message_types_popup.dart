import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart' hide Trans;
import 'package:gopal/core/localization/strings.dart';
import 'package:gopal/core/style/repo.dart';
import 'package:gopal/features/widgets/general_componenets/svg_icon.dart';

import '../controller.dart';

class MessageTypesPopup extends StatelessWidget {
  final double dy;
  const MessageTypesPopup({super.key, required this.dy});

  @override
  Widget build(BuildContext context) {
    ChatRoomPageController controller = Get.find();
    return Material(
      color: Colors.transparent,
      child: Stack(
        children: [
          GestureDetector(
            onTap: () => controller.closeMessageTypesPopup(),
            child: Container(
              color: StyleRepo.black.withValues(alpha: .1),
              child: const SizedBox.expand(),
            ),
          ),
          Positioned(
            bottom: 60 + MediaQuery.of(context).viewInsets.bottom,
            right: 0,
            left: 0,
            child: Container(
              margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: StyleRepo.grey.shade50,
                border: Border.all(color: StyleRepo.blueViolet),
                borderRadius: BorderRadius.circular(20),
              ),
              child: IconTheme(
                data: IconTheme.of(context).copyWith(color: StyleRepo.white),
                child: LayoutBuilder(
                  builder: (context, constraints) {
                    return Wrap(
                      runSpacing: 12,
                      children: [
                        InkWell(
                          onTap: () => controller.pickImages(),
                          child: SizedBox(
                            width: constraints.maxWidth / 4,
                            child: Column(
                              children: [
                                Container(
                                  height: 50,
                                  width: 50,
                                  padding: const EdgeInsets.all(12),
                                  decoration: BoxDecoration(
                                    color: StyleRepo.blueViolet,
                                    shape: BoxShape.circle,
                                    boxShadow: [
                                      BoxShadow(
                                        color: StyleRepo.black.withValues(
                                          alpha: .25,
                                        ),
                                        blurRadius: 10,
                                      ),
                                    ],
                                  ),
                                  child: SvgIcon(Assets.icons.gallery.path),
                                ),
                                Text(LocaleKeys.photos.tr()),
                              ],
                            ),
                          ),
                        ),
                        InkWell(
                          onTap: () => controller.pickImageFromCamera(),
                          child: SizedBox(
                            width: constraints.maxWidth / 4,
                            child: Column(
                              children: [
                                Container(
                                  height: 50,
                                  width: 50,
                                  padding: const EdgeInsets.all(12),
                                  decoration: BoxDecoration(
                                    color: StyleRepo.yellow.shade600,
                                    shape: BoxShape.circle,
                                    boxShadow: [
                                      BoxShadow(
                                        color: StyleRepo.black.withValues(
                                          alpha: .25,
                                        ),
                                        blurRadius: 10,
                                      ),
                                    ],
                                  ),
                                  child: SvgIcon(Assets.icons.camera.path),
                                ),
                                Text(LocaleKeys.camera.tr()),
                              ],
                            ),
                          ),
                        ),
                        InkWell(
                          onTap: () => controller.pickVideo(),
                          child: SizedBox(
                            width: constraints.maxWidth / 4,
                            child: Column(
                              children: [
                                Container(
                                  height: 50,
                                  width: 50,
                                  padding: const EdgeInsets.all(12),
                                  decoration: BoxDecoration(
                                    color: StyleRepo.berkeleyBlue,
                                    shape: BoxShape.circle,
                                    boxShadow: [
                                      BoxShadow(
                                        color: StyleRepo.black.withValues(
                                          alpha: .25,
                                        ),
                                        blurRadius: 10,
                                      ),
                                    ],
                                  ),
                                  child: const Icon(
                                    Icons.video_collection_outlined,
                                  ),
                                ),
                                Text(LocaleKeys.video.tr()),
                              ],
                            ),
                          ),
                        ),
                        InkWell(
                          onTap: () => controller.pickDocument(),
                          child: SizedBox(
                            width: constraints.maxWidth / 4,
                            child: Column(
                              children: [
                                Container(
                                  height: 50,
                                  width: 50,
                                  padding: const EdgeInsets.all(12),
                                  decoration: BoxDecoration(
                                    color: StyleRepo.turquoise,
                                    shape: BoxShape.circle,
                                    boxShadow: [
                                      BoxShadow(
                                        color: StyleRepo.black.withValues(
                                          alpha: .25,
                                        ),
                                        blurRadius: 10,
                                      ),
                                    ],
                                  ),
                                  child: SvgIcon(Assets.icons.document.path),
                                ),
                                Text(LocaleKeys.document.tr()),
                              ],
                            ),
                          ),
                        ),
                        InkWell(
                          onTap: () => controller.pickLocation(),
                          child: SizedBox(
                            width: constraints.maxWidth / 4,
                            child: Column(
                              children: [
                                Container(
                                  height: 50,
                                  width: 50,
                                  padding: const EdgeInsets.all(12),
                                  decoration: BoxDecoration(
                                    color: StyleRepo.blueViolet,
                                    shape: BoxShape.circle,
                                    boxShadow: [
                                      BoxShadow(
                                        color: StyleRepo.black.withValues(
                                          alpha: .25,
                                        ),
                                        blurRadius: 10,
                                      ),
                                    ],
                                  ),
                                  child: SvgIcon(
                                    Assets.icons.locationIcon.path,
                                  ),
                                ),
                                Text(LocaleKeys.location.tr()),
                              ],
                            ),
                          ),
                        ),
                      ],
                    );
                  },
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
