import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:gopal/core/config/app_builder.dart';
import 'package:gopal/core/config/role.dart';
import 'package:gopal/core/localization/strings.dart';
import 'package:gopal/core/routes.dart';
import 'package:gopal/features/home/<USER>';
import 'package:gopal/features/main/models/destinations.dart';
import 'package:gopal/features/main/widgets/drawer.dart';
import 'package:gopal/features/widgets/general_componenets/buttons/elevated_button.dart';
import 'package:gopal/features/widgets/general_componenets/buttons/outlined_button.dart';
import 'package:hotspot/hotspot.dart';

import '../categories/categories/index.dart';
import '../activities/my_activities/index.dart';
import '../home/<USER>';
import '../notifications/index.dart';
import '../profile/my_profile/index.dart';
import '../settings/usecases/support/widgets/support_fab.dart';
import 'controller.dart';
import 'widgets/bottom_nav_bar.dart';

class MainPage extends StatelessWidget {
  const MainPage({super.key});

  @override
  Widget build(BuildContext context) {
    AppBuilder appBuilder = Get.find();
    MainPageController controller = Get.put(MainPageController());

    return HotspotProvider(
      dismissibleSkrim: false,
      actionBuilder: (context, hotspotController) {
        return Padding(
          padding: const EdgeInsets.all(8.0),
          child: SizedBox(
            height: 35,
            child: Row(
              children: [
                Expanded(
                  child: AppOutlinedButton(
                    onTap: () {
                      hotspotController.dismiss();
                      if (appBuilder.role is NewGuestUser) {
                        appBuilder.setRole(Guest());
                      }
                    },
                    child: Text(tr(LocaleKeys.skip)),
                  ),
                ),
                Expanded(
                  child: Text(
                    "${hotspotController.index + 1}/${hotspotController.pages}",
                    textAlign: TextAlign.center,
                  ),
                ),
                Expanded(
                  child: AppElevatedButton(
                    onPressed: () async {
                      if (hotspotController.index ==
                          hotspotController.pages - 1) {
                        hotspotController.dismiss();
                        if (appBuilder.role is NewGuestUser) {
                          appBuilder.setRole(Guest());
                        }
                        return;
                      }
                      HomePageController homeController = Get.find();

                      int index = hotspotController.index;

                      if ([
                        0,
                        1,
                        (homeController.offeredActivities.isEmpty ? 3 : 4),
                        7,
                      ].contains(index)) {
                        hotspotController.next();
                        return;
                      }

                      GlobalKey? itemKey;
                      if (index == 2 &&
                          homeController.offeredActivities.isNotEmpty) {
                        itemKey = homeController.todaysOffersKey;
                      } else if (index <= 3) {
                        itemKey = homeController.centersKey;
                      } else if (index <=
                          (homeController.offeredActivities.isEmpty ? 4 : 5)) {
                        itemKey = homeController.seasonalActivitiesKey;
                      } else if (index <=
                          (homeController.offeredActivities.isEmpty ? 5 : 6)) {
                        itemKey = homeController.familyActivitiesKey;
                      }

                      if (itemKey != null) {
                        await Scrollable.ensureVisible(
                          itemKey.currentContext!,
                          duration: 200.milliseconds,
                          curve: Curves.linear,
                          alignmentPolicy:
                              ScrollPositionAlignmentPolicy.keepVisibleAtEnd,
                        );
                      }

                      WidgetsBinding.instance.addPostFrameCallback((_) {
                        hotspotController.next();
                      });
                    },
                    child: Text(
                      tr(
                        hotspotController.index == hotspotController.pages - 1
                            ? LocaleKeys.done
                            : LocaleKeys.next,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        );
      },
      child: Scaffold(
        bottomNavigationBar: const MainNavBar(),
        endDrawer: const HomeDrawer(),
        floatingActionButton: Obx(() {
          if (controller.currentDestination == MainDestinations.home) {
            return SupportFab(
              page: Pages.home,
              data: controller.currentDestination.name,
            ).withHotspot(
              order: 9,
              title: tr(LocaleKeys.send_message_to_admin),
              text: tr(LocaleKeys.support_subtitle),
            );
          } else {
            return const SizedBox();
          }
        }),
        body: Obx(() {
          Widget currentPage;
          switch (controller.currentDestination) {
            case MainDestinations.home:
              currentPage = const HomePage();
            case MainDestinations.categories:
              currentPage = const CategoriesPage();
            case MainDestinations.my_activities:
              currentPage = const MyActivitiesPage();
            case MainDestinations.notifications:
              currentPage = const NotificationsPage();
            case MainDestinations.profile:
              currentPage = const ProfilePage();
          }
          return AnimatedSwitcher(
            duration: 300.milliseconds,
            transitionBuilder:
                (child, animation) =>
                    FadeTransition(opacity: animation, child: child),
            child: currentPage,
          );
        }),
      ),
    );
  }
}
