import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:gopal/core/config/defaults.dart';
import 'package:gopal/core/localization/strings.dart';
import 'package:gopal/core/services/state_management/widgets/obs_widget.dart';
import 'package:gopal/core/style/repo.dart';
import 'package:gopal/features/widgets/general_componenets/app_bars/general_app_bar.dart';

import 'controller.dart';

class CentersOnMapPage extends StatelessWidget {
  const CentersOnMapPage({super.key});

  @override
  Widget build(BuildContext context) {
    // ignore: unused_local_variable
    CentersOnMapPageController controller = Get.put(
      CentersOnMapPageController(),
    );
    return Scaffold(
      appBar: GeneralAppBar(title: Text(tr(LocaleKeys.discover_centers))),
      body: Stack(
        children: [
          Obx(() {
            controller.markers.status;
            return GoogleMap(
              onMapCreated:
                  (mapController) => controller.mapController = mapController,
              markers: controller.markers.value?.toSet() ?? {},
              initialCameraPosition: CameraPosition(
                target: controller.location ?? Default.defaultLocation,
                zoom: 12,
              ),
              myLocationEnabled: true,
              myLocationButtonEnabled: true,
            );
          }),
          ObsListBuilder(
            obs: controller.markers,
            loader:
                (context) => Positioned(
                  top: 0,
                  right: 0,
                  left: 0,
                  child: LinearProgressIndicator(),
                ),
            errorBuilder:
                (context, error) => Positioned(
                  top: 0,
                  right: 0,
                  left: 0,
                  child: AnimatedSwitcher(
                    duration: 300.milliseconds,
                    child: Container(
                      width: double.infinity,
                      padding: EdgeInsets.symmetric(horizontal: 16),
                      decoration: BoxDecoration(
                        color: StyleRepo.white,
                        borderRadius: BorderRadius.vertical(
                          bottom: Radius.circular(15),
                        ),
                        boxShadow: StyleRepo.elevation,
                      ),
                      child: Row(
                        spacing: 12,
                        children: [
                          Expanded(child: Text(error)),
                          IconButton(
                            onPressed: controller.refreshMarkers,
                            icon: Icon(Icons.refresh),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
            builder: (_, __) => SizedBox(),
          ),
        ],
      ),
    );
  }
}
