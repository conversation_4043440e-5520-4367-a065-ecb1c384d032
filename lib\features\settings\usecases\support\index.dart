import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart' hide Trans;
import 'package:gopal/core/localization/strings.dart';
import 'package:gopal/core/models/constants/support_number.dart';
import 'package:gopal/core/routes.dart';
import 'package:gopal/core/style/assets/gen/assets.gen.dart';
import 'package:gopal/core/style/repo.dart';
import 'package:gopal/core/utils/social_urls.dart';
import 'package:gopal/core/utils/validator.dart';
import 'package:gopal/features/usecases/toast/toast.dart';
import 'package:gopal/features/widgets/general_componenets/buttons/elevated_button.dart';
import 'package:gopal/features/widgets/general_componenets/buttons/outlined_button.dart';
import 'package:url_launcher/url_launcher.dart';

import 'controller.dart';

class SupportBottomSheet extends StatelessWidget {
  final Pages page;
  final dynamic data;
  const SupportBottomSheet({super.key, required this.page, this.data});

  @override
  Widget build(BuildContext context) {
    // ignore: unused_local_variable
    SupportController controller = Get.put(SupportController(page, data));
    return SizedBox(
      height: Get.height * .7,
      child: Stack(
        alignment: Alignment.topCenter,
        children: [
          Container(
            margin: const EdgeInsets.only(top: 40),
            padding: const EdgeInsets.fromLTRB(16, 40, 16, 16),
            width: double.infinity,
            decoration: const BoxDecoration(
              color: StyleRepo.white,
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(45),
                topRight: Radius.circular(45),
              ),
            ),
            child: Column(
              children: [
                Text(
                  LocaleKeys.send_message_to_admin.tr(),
                  style: context.textTheme.headlineSmall!.copyWith(
                    color: StyleRepo.berkeleyBlue,
                    fontWeight: FontWeight.w700,
                  ),
                ),
                const Gap(12),
                Text(
                  LocaleKeys.support_subtitle.tr(),
                  style: context.textTheme.bodyMedium!.copyWith(
                    color: StyleRepo.grey.shade600,
                  ),
                ),
                const Gap(24),
                Form(
                  key: controller.form,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      TextFormField(
                        controller: controller.noteController,
                        validator:
                            (String? value) => Validator.lengthValidation(
                              value,
                              min: 10,
                              max: 300,
                            ),
                        minLines: 4,
                        maxLines: 5,
                        decoration: InputDecoration(
                          hintText: LocaleKeys.write_your_note.tr(),
                        ),
                      ),
                    ],
                  ),
                ),
                const Gap(24),
                AppElevatedButton(
                  onPressed: () => controller.confirm(),
                  child: Text(LocaleKeys.confirm.tr()),
                ),
                const Gap(12),
                AppOutlinedButton(
                  onTap: () async {
                    Uri uri = Uri.parse(
                      SocialUrls.whatsappUrl(phone: SupportNumber.number),
                    );
                    try {
                      launchUrl(uri);
                    } catch (e) {
                      Toast.show(message: "Can't launch Whatsapp");
                    }
                  },
                  child: Text(tr(LocaleKeys.contact_us_on_whatsapp)),
                ),
              ],
            ),
          ),
          Hero(
            tag: "support_sticker",
            child: Assets.icons.supportSticker.svg(),
          ),
        ],
      ),
    );
  }
}
