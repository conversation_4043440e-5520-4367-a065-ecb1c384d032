// ignore_for_file: constant_identifier_names

import 'package:flutter/material.dart';
import 'package:get/route_manager.dart';
import 'package:gopal/features/profile/member_card/index.dart';
import 'package:gopal/features/settings/invitation/index.dart';

import '../features/activities/activities/bindings.dart';
import '../features/activities/activities/index.dart';
import '../features/activities/activitiy_details/bindings.dart';
import '../features/activities/activitiy_details/index.dart';
import '../features/activities/cart/cart/index.dart';
import '../features/activities/main_activity/bindings.dart';
import '../features/activities/main_activity/index.dart';
import '../features/auth/create_account/bindings.dart';
import '../features/auth/create_account/index.dart';
import '../features/auth/login/index.dart';
import '../features/auth/verification/bindings.dart';
import '../features/auth/verification/index.dart';
import '../features/auth/boarding/index.dart';
import '../features/books/book_details/bindings.dart';
import '../features/books/book_details/index.dart';
import '../features/categories/category/bindings.dart';
import '../features/categories/category/index.dart';
import '../features/center/centers/bindings.dart';
import '../features/center/centers/index.dart';
import '../features/center/centers_on_map/index.dart';
import '../features/center/profile/bindings.dart';
import '../features/center/reviews/bindings.dart';
import '../features/center/reviews/index.dart';
import '../features/chat/preview/document/bindings.dart';
import '../features/chat/preview/document/index.dart';
import '../features/chat/preview/images/bindings.dart';
import '../features/chat/preview/images/index.dart';
import '../features/chat/preview/location/index.dart';
import '../features/chat/preview/video/bindings.dart';
import '../features/chat/preview/video/index.dart';
import '../features/chat/room/bindings.dart';
import '../features/chat/room/index.dart';
import '../features/chat/rooms/index.dart';
import '../features/profile/create_child/bindings.dart';
import '../features/profile/create_child/index.dart';
import '../features/filters/index.dart';
import '../features/main/index.dart';
import '../features/notifications/index.dart';
import '../features/profile/change_phone/index.dart';
import '../features/profile/child_profile/bindings.dart';
import '../features/profile/child_profile/index.dart';
import '../features/profile/edit_profile/bindings.dart';
import '../features/profile/edit_profile/index.dart';
import '../features/center/profile/index.dart';
import '../features/profile/member_card/binding.dart';
import '../features/profile/points_transactions/index.dart';
import '../features/search/index.dart';
import '../features/settings/about/index.dart';
import '../features/settings/help/index.dart';
import '../features/settings/privacy/index.dart';
import '../features/settings/support_replies/index.dart';
import '../features/settings/terms/index.dart';
import '../features/widgets/coming_soon.dart';
import 'config/role_middleware.dart';

abstract class AppRouting {
  static GetPage unknownRoute = GetPage(
    name: Pages.coming_soon.value,
    page: () => const ComingSoon(),
  );

  static List<GetPage<dynamic>> routes() => [
    GetPage(name: Pages.onboarding.value, page: () => const BoardingPage()),
    GetPage(name: Pages.login.value, page: () => const LoginPage()),
    GetPage(
      name: Pages.verification.value,
      binding: VerificationPageBindings(),
      page: () => const VerificationPage(),
    ),
    GetPage(
      name: Pages.create_new_account.value,
      binding: CreateAccountPageBindings(),
      page: () => const CreateAccountPage(),
    ),
    GetPage(
      name: Pages.create_child.value,
      binding: CreateChildPageBindings(),
      page: () => const CreateChildPage(),
    ),
    GetPage(name: Pages.home.value, page: () => const MainPage()),
    GetPage(
      name: Pages.activities.value,
      page: () => const ActivitiesPage(),
      binding: ActivitiesPageBindings(),
    ),
    GetPage(
      name: Pages.main_activity.value,
      preventDuplicates: false,
      binding: MainActivityPageBinding(),
      page: () => const MainActivityPage(),
    ),
    GetPage(
      name: Pages.activity_details.value,
      binding: ActivityPageBindings(),
      middlewares: [TransformToGuest()],
      preventDuplicates: false,
      page: () => ActivityDetailsPage(id: Get.arguments),
    ),
    GetPage(
      name: Pages.book.value,
      binding: BookPageBindings(),
      preventDuplicates: false,
      page: () => BookDetailsPage(id: Get.arguments),
      middlewares: [BlockGuest()],
    ),
    GetPage(
      name: Pages.edit_profile.value,
      binding: EditProfilePageBindings(),
      page: () => const EditProfilePage(),
      middlewares: [BlockGuest()],
    ),
    GetPage(
      name: Pages.child_profile.value,
      binding: ChildProfilePageBindings(),
      page: () => const ChildProfilePage(),
      middlewares: [BlockGuest()],
    ),
    GetPage(
      name: Pages.change_phone.value,
      page: () => const ChangePhonePage(),
      middlewares: [BlockGuest()],
    ),
    GetPage(
      name: Pages.center.value,
      binding: CenterProfilePageBindings(),
      page: () => CenterProfilePage(),
    ),
    GetPage(
      name: Pages.center_reviews.value,
      binding: CenterReviewsPageBindings(),
      page: () => const CenterReviewsPage(),
    ),
    GetPage(
      name: Pages.centers.value,
      binding: CentersPageBindings(),
      page: () => const CentersPage(),
    ),
    GetPage(
      name: Pages.centers_on_map.value,
      page: () => const CentersOnMapPage(),
    ),
    GetPage(
      name: Pages.category.value,
      binding: CategoryPageBindings(),
      page: () => const CategoryPage(),
    ),
    GetPage(name: Pages.filters.value, page: () => const FiltersPage()),
    GetPage(name: Pages.terms.value, page: () => const TermsPage()),
    GetPage(
      name: Pages.privacy_policy.value,
      page: () => const PrivacyPolicyPage(),
    ),
    GetPage(name: Pages.about.value, page: () => const AboutPage()),
    GetPage(name: Pages.help.value, page: () => const HelpPage()),
    GetPage(
      name: Pages.support_replies.value,
      page: () => const SupportRepliesPage(),
      middlewares: [BlockGuest()],
    ),
    GetPage(
      name: Pages.chat_rooms.value,
      page: () => const ChatRoomsPage(),
      middlewares: [BlockGuest()],
    ),
    GetPage(
      name: Pages.chat_room.value,
      page: () => const ChatRoomPage(),
      binding: ChatRoomPageBinding(),
      middlewares: [BlockGuest()],
    ),
    GetPage(
      name: Pages.images_preview.value,
      page: () => const ImagesPreviewPage(),
      binding: ImagesPreviewPageBindings(),
      middlewares: [BlockGuest()],
    ),
    GetPage(
      name: Pages.video_preview.value,
      page: () => const VideoPreviewPage(),
      binding: VideoPreviewPageBindings(),
      middlewares: [BlockGuest()],
    ),
    GetPage(
      name: Pages.document_preview.value,
      page: () => const DocumentPreviewPage(),
      binding: DocumentPreviewPageBindings(),
      middlewares: [BlockGuest()],
    ),
    GetPage(
      name: Pages.pick_location.value,
      page: () => const PickLocationPage(),
    ),
    GetPage(
      name: Pages.notifications.value,
      page: () => const NotificationsPage(),
      middlewares: [BlockGuest()],
    ),
    GetPage(
      name: Pages.invitation.value,
      page: () => const InvitationPage(),
      middlewares: [BlockGuest()],
    ),
    GetPage(
      name: Pages.cart.value,
      page: () => const CartPage(),
      middlewares: [BlockGuest()],
    ),
    GetPage(
      name: Pages.member_card.value,
      binding: MemberCardPageBinding(),
      page: () => const MemberCardPage(),
      middlewares: [BlockGuest()],
    ),
    GetPage(
      name: Pages.points_history.value,
      page: () => const PointsTransactionsPage(),
      middlewares: [BlockGuest()],
    ),
    GetPage(name: Pages.search.value, page: () => const SearchPage()),
  ];
}

enum Pages {
  coming_soon,
  onboarding,
  login,
  verification,
  create_new_account,
  create_child,
  // ==== Core ====
  home,
  activities,
  activity_details,
  main_activity,
  category,
  center,
  centers,
  centers_on_map,
  filters,
  book,
  center_reviews,
  search,
  cart,
  // === Profile ===
  edit_profile,
  child_profile,
  change_phone,
  member_card,
  points_history,
  // === Settings ===
  terms,
  privacy_policy,
  about,
  help,
  support_replies,
  invitation,
  // === Chat ===
  chat_rooms,
  chat_room,
  images_preview,
  video_preview,
  document_preview,
  pick_location,
  // === Notifications ===
  notifications;

  String get value => '/$name';

  static Pages fromValue(String value) {
    for (var page in Pages.values) {
      if (value == page.value) {
        return page;
      }
    }
    throw "unsupported page name";
  }
}

abstract class Nav {
  static Future? to(
    Pages page, {
    dynamic arguments,
    bool preventDuplicates = true,
  }) => Get.toNamed(
    page.value,
    arguments: arguments,
    preventDuplicates: preventDuplicates,
  );

  static Future? replacement(Pages page, {dynamic arguments}) =>
      Get.offNamed(page.value, arguments: arguments);

  static Future? offAll(Pages page, {dynamic arguments}) =>
      Get.offNamed(page.value, arguments: arguments);

  static Future? offUntil(
    Pages page,
    bool Function(Route<dynamic>) predicate, {
    dynamic arguments,
  }) => Get.offNamedUntil(page.value, predicate, arguments: arguments);
}
