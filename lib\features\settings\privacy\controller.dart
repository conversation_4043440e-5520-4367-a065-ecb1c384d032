import 'package:get/get.dart';
import 'package:gopal/core/services/rest_api/rest_api.dart';
import 'package:gopal/core/services/state_management/observable_variable.dart';

class PrivacyPolicyPageController extends GetxController {
  ObsVar<String> privacy = ObsVar(null);

  fetchPrivacy() async {
    ResponseModel response = await APIService.instance.requestAPI(
      Request(
        endPoint: EndPoints.settings,
        params: {"type": "PRIVACY_AND_POLICY"},
        fromJson: (json) => json['text'],
      ),
    );
    if (response.success) {
      privacy.value = response.data.first;
    } else {
      privacy.error = response.message;
    }
  }

  @override
  void onInit() {
    fetchPrivacy();
    super.onInit();
  }
}
