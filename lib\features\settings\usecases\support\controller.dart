import 'package:easy_localization/easy_localization.dart';
import 'package:get/get.dart' hide Trans;
import 'package:flutter/material.dart';
import 'package:gopal/core/localization/strings.dart';
import 'package:gopal/core/routes.dart';
import 'package:gopal/core/widgets/loading.dart';
import 'package:gopal/features/usecases/toast/status.dart';
import 'package:gopal/features/usecases/toast/toast.dart';

import '../../../../core/services/rest_api/rest_api.dart';

class SupportController extends GetxController {
  final Pages page;
  final dynamic data;

  SupportController(this.page, this.data);

  final form = GlobalKey<FormState>();

  TextEditingController noteController = TextEditingController();

  confirm() async {
    if (!form.currentState!.validate()) return;

    Map body = {"question": noteController.text};
    if (page == Pages.activity_details) {
      body["targetable_type"] = "SUB_ACTIVITY";
      body["targetable_id"] = data;
    } else if (page == Pages.center) {
      body["targetable_type"] = "PROVIDER";
      body["targetable_id"] = data;
    } else {
      body['targetable_type'] = "PAGE";
      body['targetable_id'] = page.name;
    }

    Loading.show();
    ResponseModel response = await APIService.instance.requestAPI(
      Request(
        endPoint: EndPoints.targeted_question,
        method: RequestMethod.Post,
        body: body,
      ),
    );
    Loading.dispose();
    if (response.success) {
      Get.back();
      Toast.show(
        status: ToastStatus.success,
        message: LocaleKeys.your_note_added_successfully.tr(),
      );
    } else {
      Toast.show(message: response.message);
    }
  }
}
