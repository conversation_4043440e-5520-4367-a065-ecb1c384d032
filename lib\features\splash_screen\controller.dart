import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../core/config/app_builder.dart';

class SplashScreenController extends GetxController {
  AppBuilder appBuilder = Get.find();

  loadData() async {
    WidgetsBinding.instance.addPostFrameCallback((_) async {
      DateTime before = DateTime.now();
      final nav = await appBuilder.init();

      DateTime after = DateTime.now();
      if (after.difference(before) < 4.seconds) {
        await (4.seconds - after.difference(before)).delay();
      }

      nav?.call();
    });
  }

  @override
  void onInit() {
    loadData();
    super.onInit();
  }
}
