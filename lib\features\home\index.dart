import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart' hide Trans;
import 'package:gopal/core/localization/strings.dart';
import 'package:gopal/core/routes.dart';
import 'package:gopal/core/services/state_management/widgets/obs_widget.dart';
import 'package:gopal/features/home/<USER>/about.dart';
import 'package:gopal/features/home/<USER>/slider.dart';
import 'package:gopal/features/widgets/version_widget.dart';
import 'package:hotspot/hotspot.dart';

import '../../core/services/firebase_messaging/widgets/fcm_token.dart';
import '../widgets/horizontal_list.dart';
import 'controller.dart';
import 'widgets/app_bar.dart';
import 'widgets/categories.dart';
import 'widgets/centers_list.dart';
import 'widgets/obs_activities_list.dart';
import 'widgets/text_slider.dart';

class HomePage extends StatelessWidget {
  const HomePage({super.key});

  @override
  Widget build(BuildContext context) {
    HomePageController controller = Get.put(HomePageController());

    return Stack(
      key: controller.hotspotKey,
      alignment: Alignment.topCenter,
      children: [
        RefreshIndicator(
          onRefresh: () async => await controller.refreshData(),
          child: Padding(
            padding: EdgeInsets.only(
              top: 60 + MediaQuery.viewPaddingOf(context).top,
            ),
            child: CustomScrollView(
              controller: controller.scrollController,

              slivers: [
                SliverList(
                  delegate: SliverChildListDelegate([const HomeTextSlider()]),
                ),
                const SliverGap(8),
                SliverList(
                  delegate: SliverChildListDelegate([
                    const CategoriesListWidget(),
                  ]),
                ),

                const SliverGap(16),
                SliverList(
                  delegate: SliverChildListDelegate([const HomeSlider()]),
                ),

                const SliverGap(16),
                SliverList(
                  delegate: SliverChildListDelegate([FcmTokenWidget()]),
                ),

                const SliverGap(16),
                SliverList(
                  delegate: SliverChildListDelegate([
                    ObsListActivities(
                      key: controller.todaysOffersKey,
                      activities: controller.offeredActivities,
                      title: LocaleKeys.todays_offers.tr(),
                      seeAll: () => controller.seeAllTodaysOffersActivities(),
                      isOffer: true,
                      hotspotOrder: 4,
                      hotspotTitle: LocaleKeys.todays_offers.tr(),
                      hotspotText: LocaleKeys.todays_offers_description.tr(),
                    ),
                  ]),
                ),
                const SliverGap(16),
                SliverList(
                  delegate: SliverChildListDelegate([
                    ObsListBuilder(
                      key: controller.centersKey,
                      obs: controller.centers,
                      errorBuilder:
                          (context, error) => HorizontalListError(
                            title: LocaleKeys.centers_near_you.tr(),
                            error: error,
                            onRefresh: () => controller.refreshWhenError(),
                          ),
                      loader:
                          (_) => HorizontalListLoading(
                            title: LocaleKeys.centers_near_you.tr(),
                          ),
                      builder: (context, data) {
                        return CentersList(
                          title: LocaleKeys.centers_near_you.tr(),
                          seeAll: () => Nav.to(Pages.centers_on_map),
                          centers: data,
                        );
                      },
                    ).withHotspot(
                      order: 5,
                      title: LocaleKeys.centers_near_you.tr(),
                      text: LocaleKeys.centers_near_you_description.tr(),
                    ),
                  ]),
                ),
                const SliverGap(16),
                SliverList(
                  delegate: SliverChildListDelegate([
                    ObsListActivities(
                      activities: controller.recommendedActivities,
                      title: LocaleKeys.recommended_activities.tr(),
                      seeAll: () => controller.seeAllRecommendedActivities(),
                    ),
                  ]),
                ),
                const SliverGap(16),
                SliverList(
                  delegate: SliverChildListDelegate([
                    ObsListActivities(
                      key: controller.seasonalActivitiesKey,
                      activities: controller.seasonalActivities,
                      title: LocaleKeys.seasonal_activities.tr(),
                      seeAll: () => controller.seeAllSeasonalActivities(),
                      hotspotOrder: 7,
                      hotspotTitle: LocaleKeys.seasonal_activities.tr(),
                      hotspotText:
                          LocaleKeys.seasonal_activities_description.tr(),
                    ),
                  ]),
                ),
                const SliverGap(16),
                SliverList(
                  delegate: SliverChildListDelegate([
                    ObsListActivities(
                      key: controller.familyActivitiesKey,
                      activities: controller.familyActivities,
                      title: LocaleKeys.family_activities.tr(),
                      seeAll: () => controller.seeAllFamilyActivities(),
                      hotspotOrder: 8,
                      hotspotTitle: LocaleKeys.family_activities.tr(),
                      hotspotText:
                          LocaleKeys.family_activities_description.tr(),
                    ),
                  ]),
                ),
                const SliverGap(16),
                SliverList(
                  delegate: SliverChildListDelegate([const AboutSection()]),
                ),

                const SliverGap(16),
                SliverList(
                  delegate: SliverChildListDelegate([
                    const Center(child: VersionWidget()),
                  ]),
                ),
                const SliverGap(32),
              ],
            ),
          ),
        ),
        HomeAppBar(),
      ],
    );
  }
}
