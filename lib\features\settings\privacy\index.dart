import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_html/flutter_html.dart';
import 'package:get/get.dart' hide Trans;
import 'package:gopal/core/localization/strings.dart';
import 'package:gopal/core/services/state_management/widgets/obs_widget.dart';

import '../../widgets/general_componenets/pages/rounded_page.dart';
import 'controller.dart';

class PrivacyPolicyPage extends StatelessWidget {
  const PrivacyPolicyPage({super.key});

  @override
  Widget build(BuildContext context) {
    PrivacyPolicyPageController controller = Get.put(
      PrivacyPolicyPageController(),
    );
    return RoundedPage(
      title: Text(LocaleKeys.privacy_policy.tr()),
      child: ObsVariableBuilder(
        obs: controller.privacy,
        builder: (context, html) {
          return SingleChildScrollView(child: Html(data: html));
        },
      ),
    );
  }
}
