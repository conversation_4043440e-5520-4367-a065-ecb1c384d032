import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart';
import 'package:gopal/core/config/app_builder.dart';
import 'package:gopal/core/localization/strings.dart';
import 'package:gopal/core/models/selection.dart';
import 'package:gopal/core/services/state_management/widgets/obs_widget.dart';
import 'package:gopal/core/style/repo.dart';
import 'package:gopal/core/widgets/loading.dart';
import 'package:gopal/features/home/<USER>';

import 'controller.dart';

class CitiesFilter extends StatelessWidget {
  const CitiesFilter({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.put(CitiesFilterController());
    return ObsListBuilder(
      obs: controller.cities,
      loader: (_) => FieldLoadingWidget(),
      errorBuilder:
          (context, error) => Row(
            children: [
              IconButton(
                onPressed: controller.refreshCities,
                icon: const Icon(Icons.refresh),
              ),
              const Gap(12),
              Expanded(
                child: Text(
                  error,
                  style: const TextStyle(
                    fontWeight: FontWeight.bold,
                    // color: StyleRepo.red,
                    fontSize: 13,
                  ),
                ),
              ),
            ],
          ),
      builder: (context, cities) {
        return Theme(
          data: Theme.of(context).copyWith(
            dropdownMenuTheme: DropdownMenuThemeData(
              menuStyle: MenuStyle(),
              inputDecorationTheme: InputDecorationTheme(
                border: InputBorder.none,
                enabledBorder: InputBorder.none,
                hintStyle: TextStyle(
                  color: StyleRepo.white,
                  fontSize: 13,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ),
          child: DropdownMenu<Selection>(
            textStyle: TextStyle(
              color: StyleRepo.white,
              fontSize: 13,
              fontWeight: FontWeight.bold,
              overflow: TextOverflow.ellipsis,
            ),
            trailingIcon: Icon(
              Icons.keyboard_arrow_down_rounded,
              color: StyleRepo.white,
            ),
            hintText: tr(LocaleKeys.all),
            menuHeight: Get.height * .4,
            initialSelection: cities.firstWhereOrNull(
              (element) => element.id == Get.find<AppBuilder>().filteredCityId,
            ),
            onSelected: (value) async {
              Get.find<AppBuilder>().setfilteredCity(
                value?.id == 0 ? null : value?.id,
              );
              Loading.show();
              await Get.find<HomePageController>().refreshData();
              Loading.dispose();
            },
            dropdownMenuEntries:
                [Selection.all(tr(LocaleKeys.all)), ...cities]
                    .map((e) => DropdownMenuEntry(value: e, label: e.name))
                    .toList(),
          ),
        );
      },
    );
  }
}
