export 'models/result.dart';

import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart' hide Trans;
import 'package:gopal/core/localization/strings.dart';
import 'package:gopal/core/style/repo.dart';
import 'package:gopal/core/utils/validator.dart';
import 'package:gopal/features/widgets/general_componenets/buttons/elevated_button.dart';
import 'package:gopal/features/widgets/general_componenets/svg_icon.dart';

import 'controller.dart';
import 'models/result.dart';

class CustomSessionDialog extends StatelessWidget {
  static Future<List<DateTimeSelectionResult>?> open() async =>
      await Get.dialog(const CustomSessionDialog());

  const CustomSessionDialog({super.key});

  @override
  Widget build(BuildContext context) {
    CustomSessionDialogController controller = Get.put(
      CustomSessionDialogController(),
    );
    return Center(
      child: Padding(
        padding: EdgeInsets.symmetric(horizontal: Get.width * .1),
        child: Material(
          color: Colors.transparent,
          child: Stack(
            alignment: Alignment.topCenter,
            children: [
              Container(
                padding: const EdgeInsets.fromLTRB(16, 40, 16, 16),
                margin: const EdgeInsets.only(top: 50),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(35),
                  color: StyleRepo.white,
                ),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(
                      LocaleKeys.select_date.tr(),
                      style: context.textTheme.bodyMedium!.copyWith(
                        color: StyleRepo.grey.shade700,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    const Gap(12),
                    GestureDetector(
                      onTap: () => controller.pickDate(context),
                      child: TextFormField(
                        controller: controller.dateController,
                        style: TextStyle(color: StyleRepo.grey.shade800),
                        enabled: false,
                        decoration: InputDecoration(
                          hintText: LocaleKeys.select_date.tr(),
                          suffixIcon: Padding(
                            padding: const EdgeInsets.all(10.0),
                            child: SvgIcon(Assets.icons.calenderOutlined.path),
                          ),
                        ),
                        validator: Validator.notNullValidation,
                      ),
                    ),
                    const Gap(16),
                    Row(
                      children: [
                        Expanded(
                          child: GestureDetector(
                            onTap: () => controller.pickFrom(context),
                            child: TextFormField(
                              controller: controller.fromController,
                              style: TextStyle(color: StyleRepo.grey.shade800),
                              enabled: false,
                              decoration: InputDecoration(
                                hintText: LocaleKeys.select_time.tr(),
                                suffixIcon: Padding(
                                  padding: const EdgeInsets.all(10.0),
                                  child: SvgIcon(Assets.icons.time.path),
                                ),
                              ),
                              validator: Validator.notNullValidation,
                            ),
                          ),
                        ),
                        const Gap(6),
                        Text(LocaleKeys.to.tr()),
                        const Gap(6),
                        Expanded(
                          child: GestureDetector(
                            onTap: () => controller.pickTo(context),
                            child: TextFormField(
                              controller: controller.toController,
                              enabled: false,
                              style: TextStyle(color: StyleRepo.grey.shade800),
                              decoration: InputDecoration(
                                hintText: LocaleKeys.select_time.tr(),
                                suffixIcon: Padding(
                                  padding: const EdgeInsets.all(10.0),
                                  child: SvgIcon(Assets.icons.time.path),
                                ),
                              ),
                              validator: Validator.notNullValidation,
                            ),
                          ),
                        ),
                      ],
                    ),
                    const Gap(16),
                    AppElevatedButton(
                      onPressed: () => controller.confirm(),
                      child: Text(LocaleKeys.confirm.tr()),
                    ),
                  ],
                ),
              ),
              Assets.icons.calendarSticker.svg(),
            ],
          ),
        ),
      ),
    );
  }
}
