import 'package:get/get.dart';
import 'package:gopal/core/services/state_management/observable_variable.dart';

import '../../../core/services/rest_api/rest_api.dart';

class TermsPageController extends GetxController {
  ObsVar<String> terms = ObsVar(null);

  fetchTerms() async {
    ResponseModel response = await APIService.instance.requestAPI(
      Request(
        endPoint: EndPoints.settings,
        params: {"type": "TERMS_AND_CONDITIONS"},
        fromJson: (json) => json['text'],
      ),
    );
    if (response.success) {
      terms.value = response.data.first;
    } else {
      terms.error = response.message;
    }
  }

  @override
  void onInit() {
    fetchTerms();
    super.onInit();
  }
}
