import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:gopal/core/models/activity/main_activity.dart';
import 'package:gopal/core/services/state_management/observable_variable.dart';
import 'package:gopal/core/services/state_management/widgets/obs_widget.dart';
import 'package:gopal/features/widgets/horizontal_list.dart';
import 'package:hotspot/hotspot.dart';

import '../../activities/widgets/activities_list.dart';
import '../controller.dart';

class ObsListActivities extends GetView<HomePageController> {
  final ObsList<MainActivity> activities;
  final String title;
  final Function()? seeAll;
  final bool isOffer;

  final int? hotspotOrder;
  final String? hotspotTitle;
  final String? hotspotText;

  const ObsListActivities({
    super.key,
    required this.activities,
    required this.title,
    this.seeAll,
    this.isOffer = false,
    this.hotspotOrder,
    this.hotspotTitle,
    this.hotspotText,
  });

  @override
  Widget build(BuildContext context) {
    return ObsListBuilder(
      obs: activities,
      loader: (_) => HorizontalListLoading(title: title),
      errorBuilder:
          (_, error) => HorizontalListError(
            title: title,
            error: error,
            onRefresh: () => controller.refreshWhenError(),
          ),
      builder: (context, activities) {
        Widget list = ActivitiesList(
          title: title,
          seeAll: seeAll,
          activities: activities,
          isOffer: isOffer,
        );
        if (hotspotOrder != null && activities.isNotEmpty) {
          list = list.withHotspot(
            order: hotspotOrder!,
            title: hotspotTitle!,
            text: hotspotText!,
          );
        }
        return list;
      },
    );
  }
}
