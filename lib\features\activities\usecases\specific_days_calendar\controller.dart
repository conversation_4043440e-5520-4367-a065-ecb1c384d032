// ignore_for_file: invalid_use_of_protected_member

import 'package:get/get.dart';
import 'package:gopal/core/models/activity/session.dart';
import 'package:gopal/core/services/rest_api/rest_api.dart';
import 'package:gopal/core/services/state_management/observable_variable.dart';

import '../booking/booking.dart';

class SpecificDayController extends GetxController with Booking {
  final DateTime? minDate, maxDate;
  final int id;

  SpecificDayController({required this.id, this.minDate, this.maxDate});
  ObsVar<Map<int, List<ActivitySession>>> calendarSessions = ObsVar(null);

  final Rx<DateTime> _selectedDate = DateTime.now().obs;
  DateTime get selectedDate => _selectedDate.value;
  set selectedDate(DateTime value) {
    _selectedDate.value = value;
    if (value.month != _selectedDate.value.month) {
      onMonthChanged(value, withSelectionDate: false);
    }
  }

  onMonthChanged(DateTime date, {bool withSelectionDate = true}) {
    if (withSelectionDate) {
      _selectedDate.value = date;
    }
    loadSpecificSessions(date.year, date.month);
  }

  refreshCalendar() {
    onMonthChanged(selectedDate, withSelectionDate: false);
  }

  RxSet<int> highlightedDays = RxSet();

  Request<ActivitySession>? specificSessionsRequest;

  loadSpecificSessions(int year, int month) async {
    calendarSessions.reset();
    specificSessionsRequest?.stop();
    highlightedDays.value = {};

    specificSessionsRequest = Request(
      endPoint: EndPoints.specific_session_values,
      params: {
        "sub_activity_id": id,
        "year": selectedDate.year,
        "month": selectedDate.month,
      },
    );

    ResponseModel response = await APIService.instance.requestAPI(
      specificSessionsRequest!,
    );
    if (response.success) {
      List<ActivitySession> temp =
          response.data
              .map<ActivitySession>((e) => ActivitySession.fromJson(e))
              .toList();
      Map<int, List<ActivitySession>> map = {};
      for (var session in temp) {
        if (map[session.date.day] == null) {
          map[session.date.day] = [];
        }
        map[session.date.day]!.add(session);
      }
      highlightedDays.value = map.keys.toSet();
      calendarSessions.value = map;
      selectedDate = DateTime(
        selectedDate.year,
        selectedDate.month,
        calendarSessions.value!.keys.first,
      );
    } else if (response.errorType is! CANCELED) {
      calendarSessions.error = response.message;
    }
  }

  @override
  void onInit() {
    selectedDate =
        minDate != null && minDate!.isAfter(DateTime.now())
            ? minDate!
            : DateTime.now();
    loadSpecificSessions(selectedDate.year, selectedDate.month);
    super.onInit();
  }
}
