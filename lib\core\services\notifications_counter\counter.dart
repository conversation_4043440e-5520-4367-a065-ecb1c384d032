import 'package:flutter/widgets.dart';
import 'package:get/get.dart';

import '../../config/role_middleware.dart';
import '../rest_api/rest_api.dart';

class NotificationsCounter extends WidgetsBindingObserver {
  static final Rx<int> _notificationsCount = 0.obs;
  static int get notificationsCount {
    if (!_isInitialized) {
      init();
    }
    return _notificationsCount.value;
  }

  static set notificationsCount(int value) => _notificationsCount.value = value;

  static bool _isInitialized = false;
  static bool _isInitializing = false;
  static init() async {
    if (RoleMiddleware.checkIfGuest) {
      _isInitialized = true;
      return;
    }
    if (_isInitializing) return;
    _isInitializing = true;
    WidgetsBinding.instance.addObserver(NotificationsCounter());
    _isInitialized = await _getCount();
    _isInitializing = false;
  }

  static _getCount() async {
    ResponseModel response = await APIService.instance.requestAPI(
      Request(endPoint: EndPoints.unreadNotificationsCount),
    );
    if (response.success) {
      // notificationsCount = 5;
      notificationsCount = response.data['unseen_notifications_count'];
      return true;
    } else if (response.errorType is! UN_AUTHORIZED) {
      return false;
    } else {
      return true;
    }
  }

  static dispose() {
    notificationsCount = 0;
    _isInitialized = false;
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    if (state == AppLifecycleState.resumed) {
      _isInitialized = false;
      init();
    }
  }
}
