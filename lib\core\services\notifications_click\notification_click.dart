import 'package:get/get.dart';
import 'package:gopal/core/widgets/loading.dart';
import 'package:gopal/features/activities/my_activities/controller.dart';
import 'package:gopal/features/chat/room/controller.dart';
import 'package:url_launcher/url_launcher.dart';

import '../../../features/activities/activities/models/nav.dart';
import '../../../features/activities/my_activities/models/tabs.dart';
import '../../../features/main/controller.dart';
import '../../../features/main/models/destinations.dart';
import '../../../features/usecases/toast/toast.dart';
import '../../models/notifications/data/main_notification_data.dart';
import '../../models/notifications/notification.dart';
import '../../models/notifications/type.dart';
import '../../routes.dart';
import '../rest_api/rest_api.dart';

Future<(Pages page, dynamic arguments, bool preventDuplicates)?>
notificationClick(NotificationModel notification) async {
  switch (notification.type) {
    case NotificationType.activity:
      return (
        Pages.activity_details,
        (notification.data as ActivityNotificationData).activityId,
        false,
      );
    case NotificationType.confirm_book:
      BookAcceptedNotificationData data = notification.data!.asBookAcceptedData;
      if (data.bookId != null) {
        Loading.show();
        ResponseModel response = await APIService.instance.requestAPI(
          Request(
            endPoint: EndPoints.checkout_payment,
            method: RequestMethod.Post,
            body: {"book_transaction_id": data.bookId},
          ),
        );
        Loading.dispose();
        if (response.success) {
          if (response.data[0]['redirect_url'] != null) {
            launchUrl(
              Uri.parse(response.data[0]['redirect_url']),
              mode: LaunchMode.externalApplication,
            );
          }
        } else {
          Toast.show(message: response.message);
        }
      }
      if (Get.isRegistered<MainPageController>()) {
        while (Get.currentRoute != Pages.home.value) {
          Get.back();
        }
        Get.find<MainPageController>().currentDestination =
            MainDestinations.my_activities;
        while (!Get.isRegistered<MyActivitiesPageController>()) {
          await 500.milliseconds.delay();
        }
        Get.find<MyActivitiesPageController>().tabController.animateTo(1);
        return null;
      }
      return (
        Pages.home,
        {
          "destination": MainDestinations.my_activities,
          "activities_tab": MyActivitiesTabs.progress,
        },
        true,
      );
    case NotificationType.activity_end:
      if (Get.isRegistered<MainPageController>()) {
        while (Get.currentRoute != Pages.home.value) {
          Get.back();
        }
        Get.find<MainPageController>().currentDestination =
            MainDestinations.my_activities;
        while (!Get.isRegistered<MyActivitiesPageController>()) {
          await 500.milliseconds.delay();
        }
        Get.find<MyActivitiesPageController>().tabController.animateTo(2);
        return null;
      }
      return (
        Pages.home,
        {
          "destination": MainDestinations.my_activities,
          "activities_tab": MyActivitiesTabs.completed,
        },
        true,
      );
    case NotificationType.suggestion:
      //TODO - notification
      return (Pages.activities, ActivitiesPageNav(title: ""), true);
    case NotificationType.answer_question:
      return (Pages.support_replies, null, true);
    case NotificationType.update:
      //TODO - notification

      // launchUrl(Uri.parse());
      return null;
    case NotificationType.new_message:
      NewMessageNotificationData data =
          notification.data as NewMessageNotificationData;
      if (Get.isRegistered<ChatRoomPageController>()) {
        final roomController = Get.find<ChatRoomPageController>();
        if (roomController.chatRoom.id == data.room.id) {
          Get.back();
        } else {
          return null;
        }
      }
      return (Pages.chat_room, data.room, false);
    default:
      return null;
  }
}
