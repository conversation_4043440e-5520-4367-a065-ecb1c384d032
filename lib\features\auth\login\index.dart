import 'package:easy_localization/easy_localization.dart' as localization;
import 'package:flutter/material.dart';
import 'package:get/get.dart' hide Trans;
import 'package:gap/gap.dart';
import 'package:gopal/core/localization/strings.dart';
import 'package:gopal/core/style/assets/gen/assets.gen.dart';
import 'package:gopal/core/style/repo.dart';
import 'package:gopal/features/widgets/general_componenets/buttons/elevated_button.dart';
import 'package:gopal/features/widgets/general_componenets/buttons/outlined_button.dart';

import '../../usecases/phone_field/phone_field.dart';
import '../../widgets/backgrounds/auth_bg.dart';
import '../widgets/auth_card.dart';
import '../widgets/terms_privacy_row.dart';
import 'controller.dart';
import 'widgets/languages.dart';

class LoginPage extends StatelessWidget {
  const LoginPage({super.key});

  @override
  Widget build(BuildContext context) {
    LoginPageController controller = Get.put(LoginPageController());
    return Scaffold(
      body: AuthBackground(
        backgroundImage: Assets.images.authBackgroundTest1,
        child: Safe<PERSON>rea(
          child: Column(
            children: [
              Gap(
                MediaQuery.of(context).viewInsets.bottom > 0
                    ? (Get.height - MediaQuery.of(context).viewInsets.bottom) *
                        .1
                    : Get.height * .2,
              ),
              Expanded(
                flex: 4,
                child: SingleChildScrollView(
                  child: Column(
                    children: [
                      const LanguagesSelection(),
                      const Gap(24),
                      AuthCard(
                        child: Column(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Assets.images.logo.image(
                              width: MediaQuery.sizeOf(context).width * .3,
                            ),
                            const Gap(20),
                            Text(
                              LocaleKeys.enter_your_number.tr(),
                              style: context.textTheme.titleLarge!.copyWith(
                                color: StyleRepo.berkeleyBlue,
                              ),
                            ),
                            const Gap(10),
                            Text(
                              LocaleKeys.login_using_phone_number.tr(),
                              style: context.textTheme.bodyMedium!.copyWith(
                                color: StyleRepo.grey.shade700,
                              ),
                            ),
                            const Gap(28),
                            PhoneNumberField(
                              onCountrySelection:
                                  (country) =>
                                      controller.selectedCountry = country,
                              controller: controller.phone,
                              formKey: controller.form,
                              initCountry:
                                  controller.selectedCountry.countryCode,
                            ),
                            const Gap(28),
                            AppElevatedButton(
                              onTap:
                                  () async =>
                                      await controller.sendPhoneNumber(),
                              child: Text(LocaleKeys.confirm.tr()),
                            ),
                            const Gap(16),
                            AppOutlinedButton(
                              onTap: () => controller.guestSignin(),
                              child: Text(LocaleKeys.continue_as_a_guest.tr()),
                            ),
                            const Gap(28),
                          ],
                        ),
                      ),
                      const Gap(16),
                    ],
                  ),
                ),
              ),
              const TermsPrivacyRow(),
            ],
          ),
        ),
      ),
    );
  }
}
