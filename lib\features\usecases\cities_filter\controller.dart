import 'package:get/get.dart';
import 'package:gopal/core/models/selection.dart';
import 'package:gopal/core/services/rest_api/rest_api.dart';
import 'package:gopal/core/services/state_management/observable_variable.dart';

class CitiesFilterController extends GetxController {
  ObsList<Selection> cities = ObsList([]);

  fetchCities() async {
    ResponseModel response = await APIService.instance.requestAPI(
      Request(endPoint: EndPoints.cities, fromJson: Selection.fromJson),
    );
    if (response.success) {
      cities.value = response.data;
    } else {
      cities.error = response.message;
    }
  }

  refreshCities() async {
    cities.reset();
    await fetchCities();
  }

  @override
  void onInit() {
    fetchCities();
    super.onInit();
  }
}
