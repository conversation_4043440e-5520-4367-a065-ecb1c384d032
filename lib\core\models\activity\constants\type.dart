enum ActivityType {
  numbered_session,
  specific_days,
  periodic_days,
  open_sessions,
  custom_session,
  ticket,
  other;

  static fromString(String s) {
    switch (s) {
      case "NUMBERED_SESSION":
        return numbered_session;
      case "SPECIFIC_DAYS":
      case "SPECIFIC_DAY":
        return specific_days;
      case "PERIODIC_DAYS":
      case "PERIODIC_DAY":
        return periodic_days;
      case "OPEN_SESSION":
        return open_sessions;
      case "CUSTOM_SESSION":
      case "SUB_ACTIVITY":
        return custom_session;
      case "TICKET":
        return ticket;
      default:
        return other;
    }
  }

  String get value {
    switch (this) {
      case numbered_session:
        return "NUMBERED_SESSION";
      case specific_days:
        return "SPECIFIC_DAY";
      case periodic_days:
        return "PERIODIC_DAY";
      case open_sessions:
        return "OPEN_SESSION";
      case custom_session:
        return "CUSTOM_SESSION";
      case ticket:
        return "TICKET";
      case other:
        return "OTHER";
    }
  }
}
