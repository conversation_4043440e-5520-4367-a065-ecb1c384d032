import 'package:dio/dio.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart' hide FormData;
import 'package:gopal/core/config/app_builder.dart';
import 'package:gopal/core/config/role.dart';
import 'package:gopal/core/models/constants/gender.dart';
import 'package:gopal/core/models/user/main_user.dart';
import 'package:gopal/core/routes.dart';
import 'package:gopal/core/services/state_management/observable_variable.dart';
import 'package:gopal/features/profile/create_child/models/nav.dart';
import 'package:gopal/features/usecases/gender/choose_gender.dart';
import 'package:gopal/features/usecases/toast/toast.dart';

import '../../../core/services/rest_api/rest_api.dart';
import '../../profile/create_child/models/child_creation.dart';
import 'models/nav.dart';

class CreateAccountPageController extends GetxController {
  final CreateAccountPageNav nav;
  CreateAccountPageController(this.nav);

  AppBuilder appBuilder = Get.find();

  final form = GlobalKey<FormState>();

  TextEditingController nameController = TextEditingController();
  TextEditingController emailController = TextEditingController();
  TextEditingController invitationCodeController = TextEditingController();

  //SECTION - Date
  DateTime? birthDate;
  TextEditingController birthDateController = TextEditingController();
  selectBirthDate(DateTime date) {
    birthDate = date;
    birthDateController.text = DateFormat.yMd("en").format(birthDate!);
  }

  pickDate(context) async {
    DateTime? pickedDate = await showDatePicker(
      context: context,
      firstDate: DateTime(1900),
      lastDate: DateTime.now().subtract((365 * 14).days),
      initialDate: birthDate,
    );
    if (pickedDate != null) {
      selectBirthDate(pickedDate);
    }
  }
  //!SECTION

  //SECTION - Gender
  ObsVar<Gender> gender = ObsVar(null);

  Future<Gender?> pickGender() async {
    Gender? pickedGender = await ChooseGender.show(initialValue: gender.value);
    if (pickedGender != null) {
      gender.value = pickedGender;
    }
    return pickedGender;
  }
  //!SECTION

  final Rx<bool> _isTermsAccepted = false.obs;
  bool get isTermsAccepted => _isTermsAccepted.value;
  set isTermsAccepted(bool value) => _isTermsAccepted.value = value;

  RxList<ChildCreation> children = RxList.empty();

  addChild() async {
    ChildCreation? child = await Nav.to(
      Pages.create_child,
      arguments: CreateChildPageNav(),
    );
    if (child != null) {
      children.add(child);
    }
  }

  editChild(ChildCreation child) async {
    ChildCreation? edited = await Nav.to(
      Pages.create_child,
      arguments: CreateChildPageNav(child: child),
    );
    if (edited != null) {
      children[children.indexOf(child)] = edited;
    }
  }

  confirm() async {
    if (!form.currentState!.validate()) {
      return;
    }
    FormData data = FormData.fromMap({
      "phone": nav.phone,
      "name": nameController.text,
      "email": emailController.text.trim(),
      if (birthDate != null)
        "birthdate": DateFormat("yyyy-MM-dd", "en").format(birthDate!),
      if (gender.value != null) "gender": gender.value!.value,
      if (invitationCodeController.text.trim().isNotEmpty)
        "friend_refer_code": invitationCodeController.text,
    });
    for (int i = 0; i < children.length; i++) {
      await children[i].addToForm(data, i);
    }
    ResponseModel response = await APIService.instance.requestAPI(
      Request(
        endPoint: EndPoints.create_account,
        method: RequestMethod.Post,
        body: data,
      ),
    );
    if (response.success) {
      appBuilder.setUserData(
        role: const User(),
        token: response.data['token'],
        user: MainUser.fromJson(response.data),
      );
      await appBuilder.role.initialize();
      Nav.offUntil(appBuilder.role.landing, (_) => false);
    } else {
      Toast.show(message: response.message);
    }
  }
}
