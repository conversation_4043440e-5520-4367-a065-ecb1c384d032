import 'dart:developer' as dev;

import 'package:geolocator/geolocator.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart' hide MapType;
import 'package:url_launcher/url_launcher.dart';

import '../widgets/dialogs/location_permission_dialog.dart';
import '../widgets/loading.dart';

class LocationUtils {
  static bool isLocationRequested = false;

  static LatLng? lastLocation;

  static Future<LatLng?> getMyLocation({
    bool openSettings = false,
    bool withLoading = false,
    bool getLastLocation = true,
  }) async {
    if (getLastLocation && lastLocation != null) {
      return lastLocation;
    }
    LocationPermission permission = await Geolocator.checkPermission();

    Future Function()? activateAction;

    if (permission == LocationPermission.denied) {
      activateAction = () async => await Geolocator.requestPermission();
    }

    if (permission == LocationPermission.deniedForever) {
      activateAction = () async => await Geolocator.openAppSettings();
    }
    if (!await Geolocator.isLocationServiceEnabled()) {
      activateAction = () async => await Geolocator.openLocationSettings();
    }

    if (openSettings && activateAction != null) {
      if (!isLocationRequested) {
        final acceptRequestingPermoissoin =
            await LocationPermissionDialog.open();
        isLocationRequested = true;
        if (acceptRequestingPermoissoin) {
          var res = await activateAction.call();
          dev.log(res.toString(), name: "Location");
          if (res is LocationPermission &&
              res == LocationPermission.deniedForever) {
            await Geolocator.openAppSettings();
          }
        }
      }
    }

    try {
      if (withLoading) Loading.show();

      Position position = await Geolocator.getCurrentPosition();

      if (withLoading) Loading.dispose();

      lastLocation = LatLng(position.latitude, position.longitude);

      return LatLng(position.latitude, position.longitude);
    } catch (_) {
      if (withLoading) Loading.dispose();

      return null;
    }
  }

  static openMapApp(double lat, double lng) async {
    final Uri googleMapsUrl = Uri.parse(
      "https://www.google.com/maps/dir/?api=1&destination=$lat,$lng&travelmode=driving",
    );
    await launchUrl(googleMapsUrl);
    // try {
    //   List<AvailableMap> availableMaps = await MapLauncher.installedMaps;

    //   if (availableMaps
    //       .where((map) => map.mapType == MapType.google)
    //       .isNotEmpty) {
    //     await availableMaps
    //         .firstWhere((map) => map.mapType == MapType.google)
    //         .showDirections(destination: Coords(lat, lng));
    //   } else {
    //     await availableMaps.first.showDirections(destination: Coords(lat, lng));
    //   }
    // } catch (e) {
    //   Get.snackbar("Error", e.toString());
    // }
  }
}
