import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart' hide Trans;
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';
import 'package:gopal/core/localization/strings.dart';
import 'package:gopal/core/services/state_management/widgets/obs_widget.dart';
import 'package:gopal/features/widgets/general_componenets/pages/general_page.dart';

import 'controller.dart';
import 'widgets/all_card.dart';
import 'widgets/category_card.dart';
import 'widgets/loading.dart';

class CategoriesPage extends StatelessWidget {
  const CategoriesPage({super.key});

  @override
  Widget build(BuildContext context) {
    CategoriesPageController controller = Get.put(CategoriesPageController());
    return GeneralPage(
      titleText: LocaleKeys.categories.tr(),
      withBackground: true,
      child: SafeArea(
        child: ObsListBuilder(
          obs: controller.categories,
          onRefresh: () => controller.refreshCategories(),
          loader: (_) => const CategoriesLoading(),
          builder: (context, data) {
            return AlignedGridView.count(
              itemCount: data.length + 1,
              padding: const EdgeInsets.fromLTRB(16, 24, 16, 16),
              crossAxisCount: 3,
              mainAxisSpacing: 16,
              crossAxisSpacing: 12,
              itemBuilder:
                  (context, index) =>
                      index == 0
                          ? const AllCard()
                          : CategoryCard(category: data[index - 1]),
            );
          },
        ),
      ),
    );
  }
}
