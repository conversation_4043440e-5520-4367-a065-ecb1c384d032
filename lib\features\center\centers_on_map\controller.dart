import 'package:get/get.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:gopal/core/routes.dart';
import 'package:gopal/core/services/location.dart';
import 'package:gopal/core/services/rest_api/rest_api.dart';
import 'package:gopal/core/services/state_management/observable_variable.dart';
import 'package:gopal/features/center/centers_on_map/models/center_location.dart';
import 'package:gopal/features/center/profile/models/nav.dart';

class CentersOnMapPageController extends GetxController {
  late GoogleMapController mapController;

  LatLng? location;

  init() async {
    LatLng? result = await LocationUtils.getMyLocation(openSettings: false);
    if (result != null) {
      location = result;
    }
    fetchMarkers();
  }

  @override
  void onInit() {
    init();

    super.onInit();
  }

  ObsList<Marker> markers = ObsList([]);

  fetchMarkers() async {
    ResponseModel response = await APIService.instance.requestAPI(
      Request(
        endPoint: EndPoints.centers_on_map,

        fromJson: CenterLocation.fromJson,
      ),
    );
    if (response.success) {
      List branches = response.data;
      markers.value =
          branches
              .map(
                (branch) => Marker(
                  markerId: MarkerId("${branch.id}"),
                  position: branch.location,
                  infoWindow: InfoWindow(
                    title: branch.name,
                    onTap:
                        () => Nav.to(
                          Pages.center,
                          arguments: CenterProfilePageNav(branch.id),
                        ),
                  ),
                ),
              )
              .toList();
    } else if (response.errorType is! CANCELED) {
      markers.error = response.message;
    }
  }

  Future refreshMarkers() async {
    markers.reset();
    await fetchMarkers();
  }
}
