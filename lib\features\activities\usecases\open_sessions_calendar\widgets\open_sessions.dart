import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart' hide Trans;
import 'package:gopal/core/localization/strings.dart';
import 'package:gopal/core/models/activity/constants/price_type.dart';
import 'package:gopal/core/style/repo.dart';
import 'package:gopal/core/widgets/loading.dart';
import 'package:gopal/features/widgets/general_componenets/buttons/elevated_button.dart';
import 'package:gopal/features/widgets/general_componenets/fields/field_error.dart';

import '../../../../../core/models/activity/session.dart';
import '../controller.dart';
import 'calendar.dart';

class OpenSessions extends StatelessWidget {
  final int id;
  final PriceType priceType;

  final int? minPersons, maxPersons;
  const OpenSessions({
    super.key,
    required this.id,
    required this.priceType,
    this.minPersons,
    this.maxPersons,
  });

  @override
  Widget build(BuildContext context) {
    OpenSessionsController controller = Get.find(tag: "$id");
    return Column(
      children: [
        OpenSessionsCalendar(id: id),
        Obx(() {
          if (controller.calendarSessions.loading) {
            return AlignedGridView.count(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemCount: 4,
              crossAxisCount: 2,
              mainAxisSpacing: 8,
              crossAxisSpacing: 8,
              itemBuilder: (context, index) => const FieldLoadingWidget(),
            );
          } else if (controller.calendarSessions.hasError) {
            return FieldErrorWidget(error: controller.calendarSessions.error!);
          } else {
            List<ActivitySession> sessions =
                controller.calendarSessions.value![controller
                    .selectedDate
                    .day] ??
                [];
            return AlignedGridView.count(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemCount: sessions.length,
              crossAxisCount: 2,
              mainAxisSpacing: 8,
              crossAxisSpacing: 8,
              itemBuilder: (context, index) {
                return InkWell(
                  onTap:
                      () => controller.selectedOpenSession = sessions[index].id,
                  child: Obx(
                    () => AnimatedContainer(
                      duration: 300.milliseconds,
                      padding: const EdgeInsets.symmetric(
                        vertical: 12,
                        horizontal: 12,
                      ),
                      decoration: BoxDecoration(
                        color:
                            controller.selectedOpenSession == sessions[index].id
                                ? StyleRepo.turquoise.withValues(alpha: .3)
                                : null,
                        borderRadius: BorderRadius.circular(10),
                        border: Border.all(
                          color:
                              controller.selectedOpenSession ==
                                      sessions[index].id
                                  ? StyleRepo.blueViolet
                                  : StyleRepo.grey,
                        ),
                      ),
                      child: Column(
                        children: [
                          FittedBox(
                            fit: BoxFit.fitWidth,
                            child: Text(
                              LocaleKeys.from_to.tr(
                                args: [
                                  sessions[index].from.format(context),
                                  sessions[index].to.format(context),
                                ],
                              ),
                              textAlign: TextAlign.center,
                            ),
                          ),
                          if (sessions[index].price != null) const Gap(12),
                          if (sessions[index].price != null)
                            Text(
                              "${sessions[index].price!.toStringAsFixed(1)} ${LocaleKeys.s_a_r.tr()} ${priceType.explainedText}",
                              style: const TextStyle(
                                fontWeight: FontWeight.w600,
                                color: StyleRepo.blueViolet,
                              ),
                            ),
                        ],
                      ),
                    ),
                  ),
                );
              },
            );
          }
        }),
        const Gap(24),
        Padding(
          padding: EdgeInsets.symmetric(horizontal: Get.width * .1),
          child: Obx(
            () => AppElevatedButton(
              onPressed:
                  controller.selectedOpenSession != -1
                      ? () => controller.bookAnOpenSession(
                        activityId: id,
                        session: controller
                            .calendarSessions
                            .value![controller.selectedDate.day]!
                            .firstWhere(
                              (element) =>
                                  element.id == controller.selectedOpenSession,
                            ),
                        minPersons: minPersons,
                        maxPersons: maxPersons,
                      )
                      : null,
              child: Text(LocaleKeys.booking.tr()),
            ),
          ),
        ),
      ],
    );
  }
}
