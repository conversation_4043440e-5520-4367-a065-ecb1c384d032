import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:gopal/core/localization/strings.dart';
import 'package:gopal/core/routes.dart';
import 'package:gopal/core/style/assets/gen/assets.gen.dart';
import 'package:gopal/core/style/repo.dart';
import 'package:gopal/features/widgets/general_componenets/buttons/elevated_button.dart';

class GuestBottomSheet extends StatelessWidget {
  const GuestBottomSheet({super.key});

  @override
  Widget build(BuildContext context) {
    return Stack(
      alignment: Alignment.topCenter,
      children: [
        Container(
          margin: const EdgeInsets.only(top: 40),
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 36),
          decoration: const BoxDecoration(
            color: StyleRepo.white,
            borderRadius: BorderRadius.only(
              topLeft: Radius.circular(45),
              topRight: Radius.circular(45),
            ),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const Gap(24),
              Assets.images.logo.image(
                width: MediaQuery.sizeOf(context).width * .3,
              ),
              const Gap(24),
              Text(
                LocaleKeys.guest_message.tr(),
                style: const TextStyle(fontWeight: FontWeight.w600),
              ),
              const Gap(24),
              AppElevatedButton(
                onPressed: () => Nav.offUntil(Pages.login, (_) => false),
                child: Text(LocaleKeys.signup.tr()),
              ),
            ],
          ),
        ),
        Assets.icons.profileSticker.svg(),
      ],
    );
  }
}
