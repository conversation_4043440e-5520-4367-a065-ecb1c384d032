import 'package:get/get.dart';
import 'package:gopal/core/config/app_builder.dart';
import 'package:gopal/core/models/user/main_child.dart';
import 'package:gopal/core/routes.dart';
import 'package:gopal/core/services/state_management/observable_variable.dart';
import 'package:gopal/core/widgets/loading.dart';
import 'package:gopal/features/profile/edit_profile/models/nav.dart';
import 'package:gopal/features/profile/my_profile/models/profile.dart';
import 'package:gopal/features/usecases/toast/toast.dart';

import '../../../core/services/rest_api/rest_api.dart';
import '../create_child/models/nav.dart';
import '../child_profile/models/nav.dart';

class ProfilePageController extends GetxController {
  AppBuilder appBuilder = Get.find();

  ObsVar<MyProfile> profile = ObsVar(null);
  fetchProfile() async {
    ResponseModel response = await APIService.instance.requestAPI(
      Request(endPoint: EndPoints.my_profile, fromJson: MyProfile.fromJson),
    );
    if (response.success) {
      profile.value = response.data;

      appBuilder.setUser(profile.value);
      for (var e in profile.value!.children) {
        children.add(e);
      }
    } else {
      profile.error = response.message;
    }
  }

  refreshData() async {
    profile.reset();
    children.clear();
    await fetchProfile();
  }

  @override
  void onInit() {
    fetchProfile();
    super.onInit();
  }

  RxList<MainChild> children = RxList.empty();

  addChild() async {
    MainChild? child = await Nav.to(
      Pages.create_child,
      arguments: CreateChildPageNav(withAPI: true),
    );
    if (child != null) {
      children.add(child);
    }
  }

  openChildProfile(int index) {
    Nav.to(
      Pages.child_profile,
      arguments: ChildProfilePageNav(children[index].id!),
    );
  }

  updateChild(MainChild child) {
    int index = children.indexWhere((element) => element.id == child.id);
    index;
    children[children.indexWhere((element) => element.id == child.id)] = child;
    children.refresh();
  }

  deleteChild(index) async {
    Loading.show();
    ResponseModel response = await APIService.instance.requestAPI(
      Request(
        endPoint: EndPoints.delete_child(children[index].id!),
        method: RequestMethod.Delete,
      ),
    );
    Loading.dispose();
    if (response.success) {
      children.removeAt(index);
    } else {
      Toast.show(message: response.message);
    }
  }

  editProfile() async {
    MyProfile? edited = await Nav.to(
      Pages.edit_profile,
      arguments: EditProfilePageNav(profile.value!),
    );
    if (edited != null) {
      profile.value = edited;
      profile.refresh();
    }
  }
}
